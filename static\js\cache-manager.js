class CacheManager {
    constructor() {
        this.currentStats = null;
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadCacheStats();
    }

    bindEvents() {
        // 绑定缓存标签点击事件
        const cacheTab = document.querySelector('[data-mode="cache"]');
        if (cacheTab) {
            cacheTab.addEventListener('click', () => {
                this.showCachePanel();
            });
        }
    }

    async loadCacheStats() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        try {
            const response = await fetch('/api/cache/stats');
            if (response.ok) {
                this.currentStats = await response.json();
                this.renderCacheStats();
            }
        } catch (error) {
            console.error('加载缓存统计失败:', error);
        } finally {
            this.isLoading = false;
        }
    }

    renderCacheStats() {
        const container = document.getElementById('cacheStats');
        if (!container || !this.currentStats) return;

        const stats = this.currentStats;
        const hitRate = stats.hit_rate || 0;
        const hitRateClass = hitRate > 0.7 ? 'high' : hitRate > 0.4 ? 'medium' : 'low';
        const hitRateText = (hitRate * 100).toFixed(1) + '%';

        container.innerHTML = `
            <div class="cache-stats-grid">
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${stats.memory_cache_size || 0}</span>
                    <span class="cache-stat-label">内存缓存项目</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${stats.memory_cache_limit || 0}</span>
                    <span class="cache-stat-label">内存缓存限制</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${stats.memory_hits || 0}</span>
                    <span class="cache-stat-label">内存缓存命中</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${stats.disk_hits || 0}</span>
                    <span class="cache-stat-label">磁盘缓存命中</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${stats.memory_misses || 0}</span>
                    <span class="cache-stat-label">内存缓存未命中</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${stats.total_requests || 0}</span>
                    <span class="cache-stat-label">总请求数</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value cache-hit-rate ${hitRateClass}">${hitRateText}</span>
                    <span class="cache-stat-label">缓存命中率</span>
                </div>
                <div class="cache-stat-item">
                    <span class="cache-stat-value">${this.formatFileSize(stats.cache_dir_size || 0)}</span>
                    <span class="cache-stat-label">磁盘缓存大小</span>
                </div>
            </div>
            <div style="margin-top: 15px; padding: 10px; background: var(--secondary-color); border-radius: 6px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; text-align: center;">
                    <div>
                        <strong>搜索缓存:</strong> ${stats.search_cache_size || 0}
                    </div>
                    <div>
                        <strong>文档缓存:</strong> ${stats.document_cache_size || 0}
                    </div>
                    <div>
                        <strong>热门数据:</strong> ${stats.hot_data_cache_size || 0}
                    </div>
                </div>
            </div>
        `;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showCachePanel() {
        // 切换到缓存标签
        document.querySelectorAll('.search-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector('[data-mode="cache"]').classList.add('active');

        // 显示缓存面板
        document.getElementById('cachePanel').style.display = 'block';
        document.getElementById('searchHistoryPanel').style.display = 'none';
        document.getElementById('favoritesPanel').style.display = 'none';

        // 重新加载缓存统计
        this.loadCacheStats();
    }

    async clearCache(type) {
        if (!this.confirmClearCache(type)) return;

        try {
            const response = await fetch('/api/cache/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type })
            });

            if (response.ok) {
                const result = await response.json();
                this.showToast(result.message || '缓存清理成功', 'success');
                // 重新加载统计
                this.loadCacheStats();
            } else {
                const error = await response.json();
                this.showToast(error.error || '缓存清理失败', 'error');
            }
        } catch (error) {
            console.error('清理缓存失败:', error);
            this.showToast('清理缓存失败', 'error');
        }
    }

    confirmClearCache(type) {
        const messages = {
            'all': '确定要清理所有缓存吗？这将删除所有缓存数据。',
            'expired': '确定要清理过期缓存吗？',
            'search': '确定要清理搜索缓存吗？',
            'documents': '确定要清理文档缓存吗？',
            'hot_data': '确定要清理热门数据缓存吗？'
        };

        const message = messages[type] || '确定要清理缓存吗？';
        return confirm(message);
    }

    async preloadCache() {
        const input = document.getElementById('preloadKeywords');
        const keywordsText = input.value.trim();
        
        if (!keywordsText) {
            this.showToast('请输入要预加载的关键词', 'warning');
            return;
        }

        const keywords = keywordsText.split(',').map(k => k.trim()).filter(k => k);
        
        try {
            const response = await fetch('/api/cache/preload', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    keywords,
                    filters: {}
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.showToast(result.message || '预加载任务已启动', 'success');
                input.value = '';
            } else {
                const error = await response.json();
                this.showToast(error.error || '预加载失败', 'error');
            }
        } catch (error) {
            console.error('预加载失败:', error);
            this.showToast('预加载失败', 'error');
        }
    }

    showToast(message, type = 'info') {
        // 创建提示框
        const toast = document.createElement('div');
        const colors = {
            'success': '#4caf50',
            'error': '#f44336',
            'warning': '#ff9800',
            'info': '#2196f3'
        };
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            font-size: 14px;
            max-width: 300px;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        // 3秒后自动消失
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 全局函数，供HTML调用
function refreshCacheStats() {
    if (window.cacheManager) {
        window.cacheManager.loadCacheStats();
    }
}

function clearCache(type) {
    if (window.cacheManager) {
        window.cacheManager.clearCache(type);
    }
}

function preloadCache() {
    if (window.cacheManager) {
        window.cacheManager.preloadCache();
    }
}

// 初始化缓存管理器
document.addEventListener('DOMContentLoaded', () => {
    window.cacheManager = new CacheManager();
});