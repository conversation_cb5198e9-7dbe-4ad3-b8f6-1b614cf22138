#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本清理工具
用于清理和过滤文本中的乱码字符
"""

import re
import unicodedata
import logging

class TextCleaner:
    """文本清理器，专门用于处理乱码和异常字符"""
    
    def __init__(self):
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 定义需要清理的字符模式
        self.patterns = self._init_patterns()
        
        # 常见编码错误映射
        self.encoding_fixes = {
            'â€œ': '"',     # 常见的编码错误
            'â€': '"',
            'â€™': "'",
            'â€˜': "'",
            'â€"': '—',
            'â€"': '–',
            'Â': '',        # 不需要的字符
            'â€¦': '…',
            'â„¢': '™',
            'Â©': '©',
            'Â®': '®',
            'Â°': '°',
            'Â±': '±',
            'Â²': '²',
            'Â³': '³',
            'ï¿½': '',      # Unicode replacement character in UTF-8
            '�': '',        # Unicode replacement character
        }
        
        # 中文编码错误修复
        self.chinese_encoding_fixes = {
            'æ': '',         # 常见的中文编码错误
            'ä¸': '',
            'äº': '',
            'å': '',
            'è': '',
            'ç': '',
            'é': '',
            'ê': '',
            'ë': '',
            'î': '',
            'ï': '',
            'ô': '',
            'ö': '',
            'û': '',
            'ü': '',
        }
        
        # Office和富文本样式清理模式
        self.office_style_patterns = {
            # Microsoft Office样式 - 更宽泛的匹配
            'mso_styles': re.compile(r'mso-[^;\s">]*[^;\s">]*;?', re.IGNORECASE),
            # 字体家族声明 - 更宽泛的匹配
            'font_family': re.compile(r'font-family\s*:\s*[^;\s">]*[^;\s">]*;?', re.IGNORECASE),
            # 字体名称（方正、微软等）
            'chinese_fonts': re.compile(r'(方正|微软|华文|宋体|黑体|楷体|仿宋|隶书|幼圆|新宋体|细明体)[^;\s">]*', re.IGNORECASE),
            # CSS样式属性 - 更准确的匹配
            'css_styles': re.compile(r'style\s*=\s*["\'][^"\']*["\']', re.IGNORECASE),
            # CSS类名
            'css_classes': re.compile(r'class\s*=\s*["\'][^"\']*["\']', re.IGNORECASE),
            # 其他样式标记
            'style_attributes': re.compile(r'(color|background|margin|padding|border|width|height|text-align|font-size|font-weight|text-decoration|line-height|letter-spacing|word-spacing)\s*:\s*[^;\s">]*;?', re.IGNORECASE),
            # Word特有标记
            'word_marks': re.compile(r'<(/?)(w:|o:|v:)[^>]*>', re.IGNORECASE),
            # 空的样式标签
            'empty_tags': re.compile(r'<(span|div|p|font)[^>]*>\s*</\1>', re.IGNORECASE),
        }
    
    def _init_patterns(self):
        """初始化清理模式"""
        patterns = {}
        
        # 控制字符（除了常用的换行、制表符等）
        patterns['control_chars'] = re.compile(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]')
        
        # 零宽字符
        patterns['zero_width'] = re.compile(r'[\u200B-\u200D\uFEFF]')
        
        # 私有使用区字符
        patterns['private_use'] = re.compile(r'[\uE000-\uF8FF\U000F0000-\U000FFFFD\U00100000-\U0010FFFD]')
        
        # 非字符（Unicode non-characters）
        patterns['non_characters'] = re.compile(r'[\uFDD0-\uFDEF\uFFFE\uFFFF\U0001FFFE\U0001FFFF\U0002FFFE\U0002FFFF\U0003FFFE\U0003FFFF\U0004FFFE\U0004FFFF\U0005FFFE\U0005FFFF\U0006FFFE\U0006FFFF\U0007FFFE\U0007FFFF\U0008FFFE\U0008FFFF\U0009FFFE\U0009FFFF\U000AFFFE\U000AFFFF\U000BFFFE\U000BFFFF\U000CFFFE\U000CFFFF\U000DFFFE\U000DFFFF\U000EFFFE\U000EFFFF\U000FFFFE\U000FFFFF\U0010FFFE\U0010FFFF]')
        
        # 乱码模式 - 连续的特殊字符
        patterns['garbled_sequence'] = re.compile(r'[^\w\s\u4e00-\u9fff\u3400-\u4dbf\u20000-\u2a6df\u2a700-\u2b73f\u2b740-\u2b81f\u2b820-\u2ceaf\u2ceb0-\u2ebef\u30000-\u3134f\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\u3040-\u309f\u30a0-\u30ff\u31f0-\u31ff\u3200-\u32ff\u3300-\u33ff\uff00-\uffef\u2e80-\u2eff\u2f00-\u2fdf\u31c0-\u31ef\u2ff0-\u2fff\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\u3100-\u312f\u3130-\u318f\u3190-\u319f\u31a0-\u31bf\u31f0-\u31ff\u3200-\u32ff\u3300-\u33ff\u3400-\u4dbf\u4dc0-\u4dff\u4e00-\u9fff\ua000-\ua48f\ua490-\ua4cf\ua700-\ua71f\ua720-\ua7ff\ua960-\ua97f\ua980-\ua9df\uaa00-\uaa5f\uaa60-\uaa7f\uab00-\uab2f\uab30-\uab6f\uac00-\ud7af\uf900-\ufaff\ufe10-\ufe1f\ufe30-\ufe4f\ufe50-\ufe6f\uff00-\uffef\u0020-\u007e\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u0250-\u02af\u02b0-\u02ff\u0300-\u036f\u0370-\u03ff\u0400-\u04ff\u0500-\u052f\u0530-\u058f\u0590-\u05ff\u0600-\u06ff\u0700-\u074f\u0750-\u077f\u0780-\u07bf\u07c0-\u07ff\u0800-\u083f\u0900-\u097f\u0980-\u09ff\u0a00-\u0a7f\u0a80-\u0aff\u0b00-\u0b7f\u0b80-\u0bff\u0c00-\u0c7f\u0c80-\u0cff\u0d00-\u0d7f\u0d80-\u0dff\u0e00-\u0e7f\u0e80-\u0eff\u0f00-\u0fff\u1000-\u109f\u10a0-\u10ff\u1100-\u11ff\u1200-\u137f\u1380-\u139f\u13a0-\u13ff\u1400-\u167f\u1680-\u169f\u16a0-\u16ff\u1700-\u171f\u1720-\u173f\u1740-\u175f\u1760-\u177f\u1780-\u17ff\u1800-\u18af\u1900-\u194f\u1950-\u197f\u1980-\u19df\u19e0-\u19ff\u1a00-\u1a1f\u1a20-\u1aaf\u1b00-\u1b7f\u1b80-\u1bbf\u1bc0-\u1bff\u1c00-\u1c4f\u1c50-\u1c7f\u1c80-\u1c8f\u1cc0-\u1ccf\u1cd0-\u1cff\u1d00-\u1d7f\u1d80-\u1dbf\u1dc0-\u1dff\u1e00-\u1eff\u1f00-\u1fff\u2000-\u206f\u2070-\u209f\u20a0-\u20cf\u20d0-\u20ff\u2100-\u214f\u2150-\u218f\u2190-\u21ff\u2200-\u22ff\u2300-\u23ff\u2400-\u243f\u2440-\u245f\u2460-\u24ff\u2500-\u257f\u2580-\u259f\u25a0-\u25ff\u2600-\u26ff\u2700-\u27bf\u27c0-\u27ef\u27f0-\u27ff\u2800-\u28ff\u2900-\u297f\u2980-\u29ff\u2a00-\u2aff\u2b00-\u2bff\u2c00-\u2c5f\u2c60-\u2c7f\u2c80-\u2cff\u2d00-\u2d2f\u2d30-\u2d7f\u2d80-\u2ddf\u2de0-\u2dff\u2e00-\u2e7f\u3000-\u303f！-～\u2010-\u2015\u2018-\u201f\u2020-\u2027\u2030-\u2038\u203b-\u203e\u2047-\u2049\u204b-\u204d\u2051\u2055\u2060-\u2064\u206a-\u206f\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30fb\ufe30-\ufe44\ufe47-\ufe48\ufe50-\ufe57\ufe59-\ufe66\ufe68-\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a-\uff1b\uff1f-\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65]{3,}')
        
        # HTML实体错误
        patterns['html_entities'] = re.compile(r'&[a-zA-Z0-9]+;')
        
        # 异常Unicode序列
        patterns['invalid_unicode'] = re.compile(r'[\ud800-\udfff]')  # 代理对字符
        
        return patterns
    
    def normalize_punctuation(self, text):
        """标准化中英文标点符号 - 关键改进"""
        punctuation_map = {
            '：': ':',  # 中文冒号 -> 英文冒号
            '；': ';',  # 中文分号 -> 英文分号
            '，': ',',  # 中文逗号 -> 英文逗号
            '。': '.',  # 中文句号 -> 英文句号
            '"': '"',  # 中文左双引号
            '"': '"',  # 中文右双引号
            ''': "'",  # 中文左单引号
            ''': "'",  # 中文右单引号
        }
        
        for chinese, english in punctuation_map.items():
            text = text.replace(chinese, english)
        return text

    def clean_text(self, text, aggressive=False):
        """
        清理文本中的乱码
        
        Args:
            text (str): 要清理的文本
            aggressive (bool): 是否使用激进模式清理更多内容
        
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 确保是字符串
        if not isinstance(text, str):
            text = str(text)
        
        original_text = text
        
        try:
            # 1. 标准化Unicode
            text = unicodedata.normalize('NFKC', text)
            
            # 2. 标准化中英文标点符号 - 关键改进
            text = self.normalize_punctuation(text)
            
            # 3. 修复常见编码错误
            text = self._fix_encoding_errors(text)
            
            # 4. 移除控制字符
            text = self.patterns['control_chars'].sub('', text)
            
            # 5. 移除零宽字符
            text = self.patterns['zero_width'].sub('', text)
            
            # 6. 移除私有使用区字符
            text = self.patterns['private_use'].sub('', text)
            
            # 7. 移除非字符
            text = self.patterns['non_characters'].sub('', text)
            
            # 8. 移除无效的代理对
            text = self.patterns['invalid_unicode'].sub('', text)
            
            # 9. 清理HTML实体错误
            text = self._clean_html_entities(text)
            
            # 10. 清理Office和富文本样式标记
            text = self._clean_office_styles(text)
            
            # 11. 如果是激进模式，进行更多清理
            if aggressive:
                text = self._aggressive_clean(text)
            
            # 12. 清理多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
            # 13. 检查是否清理过度（如果清理后内容太少，返回部分原文）
            if len(text) < len(original_text) * 0.3 and len(original_text) > 50:
                # 如果清理后内容减少太多，使用温和模式重新清理
                text = self._gentle_clean(original_text)
            
            return text if text else "无内容"
            
        except Exception as e:
            self.logger.error(f"文本清理过程中发生错误: {e}")
            # 发生错误时返回简单清理的结果
            return self._simple_clean(original_text)
    
    def _fix_encoding_errors(self, text):
        """修复常见的编码错误"""
        # 修复常见编码错误
        for error, fix in self.encoding_fixes.items():
            text = text.replace(error, fix)
        
        # 修复中文编码错误（只在检测到可能的编码错误时使用）
        if any(char in text for char in self.chinese_encoding_fixes.keys()):
            for error, fix in self.chinese_encoding_fixes.items():
                text = text.replace(error, fix)
        
        return text
    
    def _clean_html_entities(self, text):
        """清理HTML实体"""
        # 常见HTML实体映射
        html_entities = {
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&apos;': "'",
            '&nbsp;': ' ',
            '&ldquo;': '"',
            '&rdquo;': '"',
            '&lsquo;': "'",
            '&rsquo;': "'",
            '&mdash;': '—',
            '&ndash;': '–',
            '&hellip;': '…',
        }
        
        for entity, replacement in html_entities.items():
            text = text.replace(entity, replacement)
        
        # 移除未知的HTML实体
        text = self.patterns['html_entities'].sub('', text)
        
        return text
    
    def _clean_office_styles(self, text):
        """清理Office和富文本样式标记"""
        try:
            # 1. 清理Microsoft Office特有样式
            text = self.office_style_patterns['mso_styles'].sub('', text)

            # 2. 清理字体家族声明
            text = self.office_style_patterns['font_family'].sub('', text)

            # 3. 清理中文字体名称
            text = self.office_style_patterns['chinese_fonts'].sub('', text)

            # 4. 清理CSS样式属性
            text = self.office_style_patterns['css_styles'].sub('', text)

            # 5. 清理CSS类名
            text = self.office_style_patterns['css_classes'].sub('', text)

            # 6. 清理其他样式属性
            text = self.office_style_patterns['style_attributes'].sub('', text)

            # 7. 清理Word特有标记
            text = self.office_style_patterns['word_marks'].sub('', text)

            # 8. 清理空的样式标签
            text = self.office_style_patterns['empty_tags'].sub('', text)

            # 9. 清理常见的Office垃圾文本
            office_junk_patterns = [
                r'mso-bidi-font-family[^;>\s"\']*;?',  # MSO双向字体
                r'mso-[^;>\s"\']*[^;>\s"\']*;?',       # 所有MSO样式
                r'方正小标宋简体[^;>\s"\']*',              # 方正字体
                r'微软雅黑[^;>\s"\']*',                   # 微软字体
                r'宋体[^;>\s"\']*',                      # 宋体
                r'font-family\s*:[^;>\s"\']*[^;>\s"\']*;?',  # 字体家族
                r'style\s*=\s*["\'][^"\']*mso[^"\']*["\']',  # 包含mso的style
                r'<\s*!\s*\[if[^\]]*\]>.*?<\s*!\s*\[endif\]\s*>',  # IE条件注释
                r'<\s*o:p\s*>.*?</\s*o:p\s*>',     # Office段落标记
                r'<\s*v:[^>]*>.*?</\s*v:[^>]*>',   # VML标记
                r'xmlns:[^=]*=[^>\s]*',             # XML命名空间
                r'class\s*=\s*["\']Mso[^"\']*["\']',  # Office CSS类
                # 额外：移除Word生成的span/font标签（保留其内部文本）
                r'</?\s*span[^>]*>',
                r'</?\s*font[^>]*>',
                # 额外：移除mso-spacerun声明
                r"mso-spacerun\s*:\s*['\"]?yes['\"]?;?",
            ]

            for pattern in office_junk_patterns:
                text = re.sub(pattern, '', text, flags=re.IGNORECASE | re.DOTALL)

            # 10. 清理多余的分号和冒号
            text = re.sub(r';+', ';', text)  # 多个分号
            text = re.sub(r':\s*;', '', text)  # 空的CSS属性
            text = re.sub(r';\s*}', '}', text)  # 样式块末尾多余分号

            # 11. 清理孤立的标点符号
            text = re.sub(r'[;:]\s*(?=[<\s])', ' ', text)

            return text

        except Exception as e:
            # 如果清理过程出错，记录错误并返回原文本
            self.logger.error(f"Office样式清理过程中发生错误: {e}")
            return text
    
    def _aggressive_clean(self, text):
        """激进模式清理"""
        # 移除连续的特殊字符序列
        text = self.patterns['garbled_sequence'].sub(' ', text)
        
        # 移除孤立的特殊字符
        text = re.sub(r'(?<!\w)[^\w\s\u4e00-\u9fff](?!\w)', ' ', text)
        
        return text
    
    def _gentle_clean(self, text):
        """温和模式清理"""
        # 只移除明显的乱码字符
        text = self.patterns['control_chars'].sub('', text)
        text = self.patterns['zero_width'].sub('', text)
        text = self.patterns['invalid_unicode'].sub('', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def _simple_clean(self, text):
        """简单清理（错误恢复用）"""
        if not text:
            return "无内容"
        
        # 只做最基本的清理
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)  # 移除控制字符
        text = re.sub(r'\s+', ' ', text)  # 规范化空白字符
        return text.strip() or "无内容"
    
    def detect_garbled_ratio(self, text):
        """
        检测文本中乱码的比例
        
        Returns:
            float: 乱码比例 (0.0 - 1.0)
        """
        if not text:
            return 0.0
        
        total_chars = len(text)
        garbled_chars = 0
        
        # 统计各种异常字符
        garbled_chars += len(self.patterns['control_chars'].findall(text))
        garbled_chars += len(self.patterns['zero_width'].findall(text))
        garbled_chars += len(self.patterns['private_use'].findall(text))
        garbled_chars += len(self.patterns['non_characters'].findall(text))
        garbled_chars += len(self.patterns['invalid_unicode'].findall(text))
        
        # 检查编码错误模式
        for error in self.encoding_fixes.keys():
            garbled_chars += text.count(error) * len(error)
        
        return min(garbled_chars / total_chars, 1.0)
    
    def is_likely_garbled(self, text, threshold=0.1):
        """
        判断文本是否可能包含乱码
        
        Args:
            text (str): 要检查的文本
            threshold (float): 乱码比例阈值
        
        Returns:
            bool: 是否可能包含乱码
        """
        return self.detect_garbled_ratio(text) > threshold


# 创建全局实例
text_cleaner = TextCleaner()

def clean_garbled_text(text, aggressive=False):
    """
    便捷函数：清理文本中的乱码
    
    Args:
        text (str): 要清理的文本
        aggressive (bool): 是否使用激进模式
    
    Returns:
        str: 清理后的文本
    """
    return text_cleaner.clean_text(text, aggressive)

def detect_garbled_text(text, threshold=0.1):
    """
    便捷函数：检测文本是否包含乱码
    
    Args:
        text (str): 要检查的文本
        threshold (float): 乱码比例阈值
    
    Returns:
        bool: 是否包含乱码
    """
    return text_cleaner.is_likely_garbled(text, threshold)
