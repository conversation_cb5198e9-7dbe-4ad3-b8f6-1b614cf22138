#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器 - 提供多层次的缓存系统
支持内存缓存、磁盘缓存和智能预加载
"""

import json
import time
import threading
import hashlib
import pickle
import os
from typing import Dict, Any, Optional, List, Tuple
from collections import OrderedDict, defaultdict
from datetime import datetime, timedelta
import logging

class CacheManager:
    """缓存管理器 - 提供高性能的多层次缓存"""
    
    def __init__(self, max_memory_items=1000, cache_dir=None):
        """
        初始化缓存管理器
        
        Args:
            max_memory_items: 内存缓存最大项目数
            cache_dir: 磁盘缓存目录
        """
        self.max_memory_items = max_memory_items
        
        # 使用持久化目录存储缓存数据
        if cache_dir is None:
            # 确定缓存目录 - 优先使用持久化目录，回退到本地目录
            render_cache_dir = "/opt/render/project/cache/app_cache"
            if os.path.exists("/opt/render/project") or os.environ.get('FLASK_ENV') == 'production':
                # 云端环境或生产环境
                cache_dir = render_cache_dir
                logging.info(f"使用云端持久化缓存目录: {cache_dir}")
            else:
                # 本地开发环境
                cache_dir = "cache/app_cache"
                logging.info(f"使用本地缓存目录: {cache_dir}")
        
        self.cache_dir = cache_dir
        
        # 内存缓存 - 使用LRU算法
        self.memory_cache = {}
        self.memory_cache_order = []  # 用于维护LRU顺序
        self.cache_stats = {
            'memory_hits': 0,
            'memory_misses': 0,
            'disk_hits': 0,
            'disk_misses': 0,
            'total_requests': 0
        }
        
        # 搜索结果缓存 - 基于查询参数
        self.search_cache = {}
        self.search_cache_ttl = 300  # 5分钟
        
        # 文档详情缓存 - 基于文档ID
        self.document_cache = {}
        self.document_cache_ttl = 1800  # 30分钟
        
        # 热门数据缓存
        self.hot_data_cache = {}
        self.hot_data_ttl = 600  # 10分钟
        
        # 用户行为追踪（用于智能预加载）
        self.user_behavior = defaultdict(list)
        self.access_patterns = defaultdict(int)
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(os.path.join(cache_dir, 'search'), exist_ok=True)
        os.makedirs(os.path.join(cache_dir, 'documents'), exist_ok=True)
        os.makedirs(os.path.join(cache_dir, 'hot_data'), exist_ok=True)
        
        # 启动清理线程
        self.start_cleanup_thread()
        
        logging.info("缓存管理器初始化完成")
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        # 将参数转换为字符串并生成哈希
        key_data = json.dumps({'args': args, 'kwargs': kwargs}, sort_keys=True)
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()
    
    def _is_expired(self, cache_item: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        if 'timestamp' not in cache_item:
            return True
        
        ttl = cache_item.get('ttl', 300)
        age = time.time() - cache_item['timestamp']
        return age > ttl
    
    def _get_cache_path(self, cache_type: str, key: str) -> str:
        """获取磁盘缓存文件路径"""
        return os.path.join(self.cache_dir, cache_type, f"{key}.cache")
    
    def _save_to_disk(self, cache_type: str, key: str, data: Any, ttl: int = 3600):
        """保存数据到磁盘缓存"""
        try:
            cache_path = self._get_cache_path(cache_type, key)
            cache_item = {
                'data': data,
                'timestamp': time.time(),
                'ttl': ttl
            }
            
            with open(cache_path, 'wb') as f:
                pickle.dump(cache_item, f)
        except Exception as e:
            logging.error(f"保存磁盘缓存失败: {e}")
    
    def _load_from_disk(self, cache_type: str, key: str) -> Optional[Any]:
        """从磁盘缓存加载数据"""
        try:
            cache_path = self._get_cache_path(cache_type, key)
            if not os.path.exists(cache_path):
                return None
            
            with open(cache_path, 'rb') as f:
                cache_item = pickle.load(f)
            
            if self._is_expired(cache_item):
                os.remove(cache_path)
                return None
            
            return cache_item['data']
        except Exception as e:
            logging.error(f"加载磁盘缓存失败: {e}")
            return None
    
    def cache_search_result(self, keyword: str, filters: Dict[str, Any], result: Dict[str, Any]):
        """缓存搜索结果"""
        with self.lock:
            key = self._generate_key(keyword, filters)
            cache_item = {
                'result': result,
                'timestamp': time.time(),
                'ttl': self.search_cache_ttl
            }
            
            # 内存缓存
            self.search_cache[key] = cache_item
            self._update_lru('search', key)
            
            # 磁盘缓存
            self._save_to_disk('search', key, result, self.search_cache_ttl)
            
            # 更新访问模式
            self.access_patterns[f"search_{keyword}"] += 1
            
            logging.debug(f"缓存搜索结果: {keyword}")
    
    def get_cached_search(self, keyword: str, filters: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取缓存的搜索结果"""
        with self.lock:
            self.cache_stats['total_requests'] += 1
            key = self._generate_key(keyword, filters)
            
            # 先检查内存缓存
            if key in self.search_cache:
                cache_item = self.search_cache[key]
                if not self._is_expired(cache_item):
                    self.cache_stats['memory_hits'] += 1
                    # LRU更新
                    self._update_lru('search', key)
                    return cache_item['result']
                else:
                    # 清理过期缓存
                    del self.search_cache[key]
                    self._remove_from_lru('search', key)
            
            # 检查磁盘缓存
            disk_result = self._load_from_disk('search', key)
            if disk_result:
                self.cache_stats['disk_hits'] += 1
                # 重新加载到内存
                self.search_cache[key] = {
                    'result': disk_result,
                    'timestamp': time.time(),
                    'ttl': self.search_cache_ttl
                }
                # LRU管理
                self._update_lru('search', key)
                return disk_result
            
            self.cache_stats['memory_misses'] += 1
            return None
    
    def cache_document(self, doc_id: str, document_data: Dict[str, Any]):
        """缓存文档详情"""
        with self.lock:
            cache_item = {
                'data': document_data,
                'timestamp': time.time(),
                'ttl': self.document_cache_ttl
            }
            
            # 内存缓存（LRU管理）
            self.document_cache[doc_id] = cache_item
            self._update_lru('document', doc_id)
            
            # 限制内存缓存大小
            if len(self.document_cache) > self.max_memory_items:
                self._evict_lru('document')
            
            # 磁盘缓存
            self._save_to_disk('documents', doc_id, document_data, self.document_cache_ttl)
            
            logging.debug(f"缓存文档: {doc_id}")
    
    def get_cached_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的文档详情"""
        with self.lock:
            self.cache_stats['total_requests'] += 1
            
            # 检查内存缓存
            if doc_id in self.document_cache:
                cache_item = self.document_cache[doc_id]
                if not self._is_expired(cache_item):
                    self.cache_stats['memory_hits'] += 1
                    # LRU更新
                    self._update_lru('document', doc_id)
                    return cache_item['data']
                else:
                    # 清理过期缓存
                    del self.document_cache[doc_id]
                    self._remove_from_lru('document', doc_id)
            
            # 检查磁盘缓存
            disk_result = self._load_from_disk('documents', doc_id)
            if disk_result:
                self.cache_stats['disk_hits'] += 1
                # 重新加载到内存
                self.document_cache[doc_id] = {
                    'data': disk_result,
                    'timestamp': time.time(),
                    'ttl': self.document_cache_ttl
                }
                # LRU管理
                self._update_lru('document', doc_id)
                return disk_result
            
            self.cache_stats['memory_misses'] += 1
            return None
    
    def cache_hot_data(self, data_type: str, data: Any):
        """缓存热门数据"""
        with self.lock:
            cache_item = {
                'data': data,
                'timestamp': time.time(),
                'ttl': self.hot_data_ttl
            }
            
            self.hot_data_cache[data_type] = cache_item
            self._save_to_disk('hot_data', data_type, data, self.hot_data_ttl)
    
    def get_hot_data(self, data_type: str) -> Optional[Any]:
        """获取热门数据缓存"""
        with self.lock:
            if data_type in self.hot_data_cache:
                cache_item = self.hot_data_cache[data_type]
                if not self._is_expired(cache_item):
                    return cache_item['data']
                else:
                    del self.hot_data_cache[data_type]
            
            # 检查磁盘缓存
            return self._load_from_disk('hot_data', data_type)
    
    def record_user_behavior(self, user_id: str, action: str, data: Dict[str, Any]):
        """记录用户行为，用于智能预加载"""
        with self.lock:
            self.user_behavior[user_id].append({
                'action': action,
                'data': data,
                'timestamp': time.time()
            })
            
            # 限制历史记录长度
            if len(self.user_behavior[user_id]) > 100:
                self.user_behavior[user_id] = self.user_behavior[user_id][-50:]
    
    def get_suggested_preload(self, user_id: str) -> List[str]:
        """基于用户行为获取预加载建议"""
        with self.lock:
            if user_id not in self.user_behavior:
                return []
            
            suggestions = []
            recent_actions = self.user_behavior[user_id][-20:]  # 最近20个动作
            
            # 分析搜索模式
            search_keywords = []
            for action in recent_actions:
                if action['action'] == 'search':
                    keyword = action['data'].get('keyword', '')
                    if keyword:
                        search_keywords.append(keyword)
            
            # 如果有重复搜索，建议预加载相关文档
            if len(search_keywords) > 1:
                suggestions.extend(search_keywords[-3:])  # 最近3个搜索词
            
            return list(set(suggestions))  # 去重
    
    def preload_search_results(self, keywords: List[str], filters: Dict[str, Any]):
        """预加载搜索结果"""
        for keyword in keywords:
            if not self.get_cached_search(keyword, filters):
                # 这里应该异步加载，但为了简单，我们只是标记需要预加载
                logging.info(f"标记预加载: {keyword}")
    
    def clear_expired_cache(self):
        """清理过期缓存"""
        with self.lock:
            current_time = time.time()
            
            # 清理内存缓存
            expired_keys = []
            for key, item in self.search_cache.items():
                if self._is_expired(item):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.search_cache[key]
            
            # 清理文档缓存
            expired_docs = []
            for doc_id, item in self.document_cache.items():
                if self._is_expired(item):
                    expired_docs.append(doc_id)
            
            for doc_id in expired_docs:
                del self.document_cache[doc_id]
            
            # 清理热门数据缓存
            expired_hot = []
            for data_type, item in self.hot_data_cache.items():
                if self._is_expired(item):
                    expired_hot.append(data_type)
            
            for data_type in expired_hot:
                del self.hot_data_cache[data_type]
            
            logging.debug(f"清理过期缓存完成: 搜索{len(expired_keys)}, 文档{len(expired_docs)}, 热门数据{len(expired_hot)}")
    
    def clear_all_cache(self):
        """清理所有缓存"""
        with self.lock:
            self.search_cache.clear()
            self.document_cache.clear()
            self.hot_data_cache.clear()
            
            # 清理磁盘缓存
            import shutil
            if os.path.exists(self.cache_dir):
                shutil.rmtree(self.cache_dir)
                os.makedirs(self.cache_dir, exist_ok=True)
                os.makedirs(os.path.join(self.cache_dir, 'search'), exist_ok=True)
                os.makedirs(os.path.join(self.cache_dir, 'documents'), exist_ok=True)
                os.makedirs(os.path.join(self.cache_dir, 'hot_data'), exist_ok=True)
            
            logging.info("所有缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.cache_stats['total_requests']
            if total_requests == 0:
                hit_rate = 0
            else:
                hit_rate = (self.cache_stats['memory_hits'] + self.cache_stats['disk_hits']) / total_requests
            
            return {
                'memory_cache_size': len(self.search_cache) + len(self.document_cache),
                'memory_cache_limit': self.max_memory_items,
                'memory_hits': self.cache_stats['memory_hits'],
                'disk_hits': self.cache_stats['disk_hits'],
                'memory_misses': self.cache_stats['memory_misses'],
                'disk_misses': self.cache_stats['disk_misses'],
                'total_requests': total_requests,
                'hit_rate': hit_rate,
                'search_cache_size': len(self.search_cache),
                'document_cache_size': len(self.document_cache),
                'hot_data_cache_size': len(self.hot_data_cache),
                'cache_dir_size': self._get_cache_dir_size()
            }
    
    def _get_cache_dir_size(self) -> int:
        """获取缓存目录大小（字节）"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(self.cache_dir):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        except:
            return 0
    
    def start_cleanup_thread(self):
        """启动后台清理线程"""
        def cleanup_worker():
            while True:
                time.sleep(60)  # 每分钟清理一次
                try:
                    self.clear_expired_cache()
                except Exception as e:
                    logging.error(f"缓存清理失败: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logging.info("缓存清理线程已启动")
    
    def _update_lru(self, cache_type: str, key: str):
        """更新LRU顺序"""
        lru_key = f"{cache_type}_{key}"
        if lru_key in self.memory_cache_order:
            self.memory_cache_order.remove(lru_key)
        self.memory_cache_order.append(lru_key)
    
    def _remove_from_lru(self, cache_type: str, key: str):
        """从LRU顺序中移除"""
        lru_key = f"{cache_type}_{key}"
        if lru_key in self.memory_cache_order:
            self.memory_cache_order.remove(lru_key)
    
    def _evict_lru(self, cache_type: str):
        """淘汰最久未使用的缓存项"""
        for lru_key in self.memory_cache_order:
            if lru_key.startswith(f"{cache_type}_"):
                key = lru_key[len(f"{cache_type}_"):]
                if cache_type == 'search' and key in self.search_cache:
                    del self.search_cache[key]
                elif cache_type == 'document' and key in self.document_cache:
                    del self.document_cache[key]
                self.memory_cache_order.remove(lru_key)
                break

# 全局缓存管理器实例
cache_manager = None

def get_cache_manager():
    """获取缓存管理器实例（单例模式）"""
    global cache_manager
    if cache_manager is None:
        cache_manager = CacheManager()
    return cache_manager