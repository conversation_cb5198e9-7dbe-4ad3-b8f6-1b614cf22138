2025-08-27 23:56:32,414 - INFO - OA爬虫实例初始化成功
2025-08-27 23:56:32,415 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:56:32,415 - INFO - 请访问: http://localhost:5035
2025-08-27 23:56:33,746 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://************:5035
2025-08-27 23:56:33,746 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 23:56:33,760 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 23:56:34,075 - INFO - OA爬虫实例初始化成功
2025-08-27 23:56:34,075 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:56:34,075 - INFO - 请访问: http://localhost:5035
2025-08-27 23:56:34,082 - WARNING -  * Debugger is active!
2025-08-27 23:56:34,087 - INFO -  * Debugger PIN: 145-413-362
2025-08-27 23:59:05,981 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,160 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,161 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,161 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,457 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 23:59:06,875 - INFO - OA爬虫实例初始化成功
2025-08-27 23:59:06,876 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:59:06,876 - INFO - 请访问: http://localhost:5035
2025-08-27 23:59:06,886 - WARNING -  * Debugger is active!
2025-08-27 23:59:06,892 - INFO -  * Debugger PIN: 145-413-362
2025-08-27 23:59:17,233 - INFO - 收到数据请求: {'page': 1, 'limit': 5, 'keyword': '通知', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-27 23:59:17,233 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-27 23:59:17,237 - INFO - 缓存清理线程已启动
2025-08-27 23:59:17,237 - INFO - 缓存管理器初始化完成
2025-08-27 23:59:17,238 - INFO - 缓存管理器初始化成功
2025-08-27 23:59:17,238 - INFO - 增强搜索请求，参数: page=1, limit=5, keyword='通知', unit='', dept='', start_date='', end_date='', author=''
2025-08-27 23:59:17,238 - INFO - 实时API请求，参数: page=1, limit=5, keyword='通知'
2025-08-27 23:59:17,239 - INFO - 正在获取第0-4条记录...
2025-08-27 23:59:17,239 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-27 23:59:17,239 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '通知', 'row_start': '0', 'row_end': '4'}
2025-08-27 23:59:17,657 - INFO - 响应状态码: 200
2025-08-27 23:59:17,657 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-27 23:59:17,658 - INFO - 响应内容长度: 59168
2025-08-27 23:59:17,658 - INFO - 检测到JSON格式响应，尝试解析
2025-08-27 23:59:17,658 - INFO - 成功解析JSON响应
2025-08-27 23:59:17,658 - INFO - 响应是数组格式，包含4条记录
2025-08-27 23:59:17,675 - ERROR - 解析第0-4条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-27 23:59:17,675 - INFO - 正在获取文档总数...
2025-08-27 23:59:17,675 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-27 23:59:17,675 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '通知'}
2025-08-27 23:59:23,009 - INFO - GetDocNum响应状态码: 200
2025-08-27 23:59:23,010 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-27 23:59:23,010 - INFO - 响应内容长度: 5
2025-08-27 23:59:23,010 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-27 23:59:23,010 - INFO - 响应内容前200字符: 29006
2025-08-27 23:59:23,010 - INFO - 使用默认文档总数: 3000
2025-08-27 23:59:23,010 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-27 23:59:23,011 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-27 23:59:23,012 - INFO - 127.0.0.1 - - [27/Aug/2025 23:59:23] "GET /api/get_oa_data?page=1&limit=5&keyword=通知 HTTP/1.1" 200 -
2025-08-27 23:59:32,465 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:32,465 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:32,469 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:32,478 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:33,103 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 23:59:33,725 - INFO - OA爬虫实例初始化成功
2025-08-27 23:59:33,725 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:59:33,726 - INFO - 请访问: http://localhost:5035
2025-08-27 23:59:33,737 - WARNING -  * Debugger is active!
2025-08-27 23:59:33,743 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:00:01,088 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:01] "[31m[1mGET / HTTP/1.1[0m" 401 -
2025-08-28 00:00:09,694 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:09] "GET / HTTP/1.1" 200 -
2025-08-28 00:00:10,321 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/css/style.css HTTP/1.1" 200 -
2025-08-28 00:00:10,383 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/favorites-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:10,384 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/cache-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:10,385 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/enhanced-search.js HTTP/1.1" 200 -
2025-08-28 00:00:10,387 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/text-cleaner-enhanced.js HTTP/1.1" 200 -
2025-08-28 00:00:10,501 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-28 00:00:10,503 - INFO - 缓存清理线程已启动
2025-08-28 00:00:10,503 - INFO - 缓存管理器初始化完成
2025-08-28 00:00:10,504 - INFO - 缓存管理器初始化成功
2025-08-28 00:00:10,504 - INFO - 实时API请求，参数: page=1, limit=200, keyword=''
2025-08-28 00:00:10,504 - INFO - 正在获取第0-199条记录...
2025-08-28 00:00:10,504 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:10,505 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '199'}
2025-08-28 00:00:10,713 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:00:10,714 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:00:10,714 - INFO - 使用本地缓存目录: cache
2025-08-28 00:00:10,714 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:00:10,714 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:00:10,714 - INFO - 正在获取第0-19条记录...
2025-08-28 00:00:10,715 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:00:10,715 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:10,715 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:00:10,728 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:00:10,729 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-28 00:00:10,730 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:00:11,101 - INFO - 响应状态码: 200
2025-08-28 00:00:11,102 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:11,102 - INFO - 响应内容长度: 295817
2025-08-28 00:00:11,103 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:11,105 - INFO - 成功解析JSON响应
2025-08-28 00:00:11,106 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:00:11,121 - ERROR - 解析第0-19条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:11,121 - INFO - 正在获取文档总数...
2025-08-28 00:00:11,122 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:11,122 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:11,327 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:11,328 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:11,328 - INFO - 响应内容长度: 5
2025-08-28 00:00:11,329 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:11,329 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:11,329 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:11,329 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-28 00:00:11,329 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-28 00:00:11,329 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:11] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:00:12,895 - INFO - 响应状态码: 200
2025-08-28 00:00:12,895 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:12,902 - INFO - 响应内容长度: 8628454
2025-08-28 00:00:12,909 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:12,944 - INFO - 成功解析JSON响应
2025-08-28 00:00:12,945 - INFO - 响应是数组格式，包含199条记录
2025-08-28 00:00:12,949 - ERROR - 解析第0-199条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:12,950 - INFO - 正在获取文档总数...
2025-08-28 00:00:12,950 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:12,950 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:13,192 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:13,192 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:13,192 - INFO - 响应内容长度: 5
2025-08-28 00:00:13,192 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:13,192 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:13,192 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:13,194 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:13] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:00:15,478 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "GET / HTTP/1.1" 200 -
2025-08-28 00:00:15,726 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,837 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,837 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,838 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,838 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,873 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:00:16,188 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:00:16,189 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:00:16,189 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:00:16,190 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:00:16,190 - INFO - 正在获取第0-19条记录...
2025-08-28 00:00:16,190 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:16,190 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:00:16,192 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:00:16,194 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:00:16,287 - INFO - 响应状态码: 200
2025-08-28 00:00:16,288 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:16,288 - INFO - 响应内容长度: 295817
2025-08-28 00:00:16,288 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:16,289 - INFO - 成功解析JSON响应
2025-08-28 00:00:16,289 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:00:16,295 - ERROR - 解析第0-19条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:16,295 - INFO - 正在获取文档总数...
2025-08-28 00:00:16,295 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:16,296 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:16,448 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:16,448 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:16,448 - INFO - 响应内容长度: 5
2025-08-28 00:00:16,448 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:16,448 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:16,448 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:16,448 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-28 00:00:16,449 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-28 00:00:16,450 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:00:19,592 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:00:19,609 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:19] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-08-28 00:00:20,780 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:20] "GET / HTTP/1.1" 200 -
2025-08-28 00:00:21,133 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/css/style.css HTTP/1.1" 200 -
2025-08-28 00:00:21,180 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-08-28 00:00:21,181 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/text-cleaner-enhanced.js HTTP/1.1" 200 -
2025-08-28 00:00:21,181 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/favorites-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:21,182 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/enhanced-search.js HTTP/1.1" 200 -
2025-08-28 00:00:21,182 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/cache-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:22,689 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:00:22,708 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:00:22,708 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:00:22,709 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:00:22,710 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:00:22,710 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:00:22,711 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:00:22,711 - INFO - 正在获取第0-19条记录...
2025-08-28 00:00:22,711 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:22,711 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:00:22,807 - INFO - 响应状态码: 200
2025-08-28 00:00:22,807 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:22,808 - INFO - 响应内容长度: 295817
2025-08-28 00:00:22,808 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:22,810 - INFO - 成功解析JSON响应
2025-08-28 00:00:22,810 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:00:22,816 - ERROR - 解析第0-19条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:22,817 - INFO - 正在获取文档总数...
2025-08-28 00:00:22,817 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:22,817 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:22,970 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:22,970 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:22,970 - INFO - 响应内容长度: 5
2025-08-28 00:00:22,970 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:22,970 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:22,971 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:22,971 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-28 00:00:22,971 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-28 00:00:22,971 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:01:58,001 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\oa_scraper.py', reloading
2025-08-28 00:01:58,003 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\oa_scraper.py', reloading
2025-08-28 00:01:58,146 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:01:58,493 - INFO - OA爬虫实例初始化成功
2025-08-28 00:01:58,493 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:01:58,493 - INFO - 请访问: http://localhost:5035
2025-08-28 00:01:58,504 - WARNING -  * Debugger is active!
2025-08-28 00:01:58,510 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:02:21,978 - INFO - OA爬虫实例初始化成功
2025-08-28 00:02:21,978 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:02:21,979 - INFO - 请访问: http://localhost:5035
2025-08-28 00:02:23,269 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://************:5035
2025-08-28 00:02:23,269 - INFO - [33mPress CTRL+C to quit[0m
2025-08-28 00:02:23,275 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:02:23,744 - INFO - OA爬虫实例初始化成功
2025-08-28 00:02:23,744 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:02:23,745 - INFO - 请访问: http://localhost:5035
2025-08-28 00:02:23,758 - WARNING -  * Debugger is active!
2025-08-28 00:02:23,761 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:02:43,273 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,354 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,355 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,355 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,888 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:02:44,308 - INFO - OA爬虫实例初始化成功
2025-08-28 00:02:44,308 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:02:44,308 - INFO - 请访问: http://localhost:5035
2025-08-28 00:02:44,324 - WARNING -  * Debugger is active!
2025-08-28 00:02:44,327 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:02:54,253 - INFO - 收到数据请求: {'page': 1, 'limit': 5, 'keyword': '通知', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:02:54,253 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-28 00:02:54,254 - INFO - 缓存清理线程已启动
2025-08-28 00:02:54,255 - INFO - 缓存管理器初始化完成
2025-08-28 00:02:54,255 - INFO - 缓存管理器初始化成功
2025-08-28 00:02:54,255 - INFO - 从缓存返回 0 条OA记录
2025-08-28 00:02:54,255 - INFO - 127.0.0.1 - - [28/Aug/2025 00:02:54] "GET /api/get_oa_data?page=1&limit=5&keyword=通知 HTTP/1.1" 200 -
2025-08-28 00:04:00,500 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "GET / HTTP/1.1" 200 -
2025-08-28 00:04:00,808 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,908 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,908 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,909 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,909 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,948 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:04:01,272 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:04:01,273 - INFO - 使用本地缓存目录: cache
2025-08-28 00:04:01,274 - INFO - 使用本地缓存目录: cache
2025-08-28 00:04:01,274 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:04:01,274 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:04:01,274 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:04:01,274 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:04:01,275 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:04:01,275 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:04:01,275 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:04:01,275 - INFO - 正在获取第0-19条记录...
2025-08-28 00:04:01,275 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:04:01,276 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:04:01,642 - INFO - 响应状态码: 200
2025-08-28 00:04:01,648 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:01,650 - INFO - 响应内容长度: 295817
2025-08-28 00:04:01,651 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:04:01,653 - INFO - 成功解析JSON响应
2025-08-28 00:04:01,656 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:04:01,676 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:01,680 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:01,685 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:01,689 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:01,692 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:01,694 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:01,697 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:01,701 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:01,709 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:01,716 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:01,723 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:01,726 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:01,729 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:01,736 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:01,737 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:01,737 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:01,740 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:01,741 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-28 00:04:01,741 - INFO - 正在获取文档总数...
2025-08-28 00:04:01,742 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:04:01,742 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:04:01,929 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:04:01,929 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:01,929 - INFO - 响应内容长度: 5
2025-08-28 00:04:01,930 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:04:01,930 - INFO - 响应内容前200字符: 39552
2025-08-28 00:04:01,930 - INFO - 使用默认文档总数: 3000
2025-08-28 00:04:01,937 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:01,939 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:01,940 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:01,942 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:01,942 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:01,943 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:01,946 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:01,947 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:01,952 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:01,959 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:01,966 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:01,969 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:01,972 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:01,978 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:01,979 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:01,980 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:01,983 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:01,983 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-28 00:04:01,983 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-28 00:04:01,990 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:04:02,326 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_jvaiga/check HTTP/1.1" 200 -
2025-08-28 00:04:02,329 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_xp372u/check HTTP/1.1" 200 -
2025-08-28 00:04:02,336 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_9yf8ve/check HTTP/1.1" 200 -
2025-08-28 00:04:02,342 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_61jokd/check HTTP/1.1" 200 -
2025-08-28 00:04:02,344 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_ow45rz/check HTTP/1.1" 200 -
2025-08-28 00:04:02,351 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_sthsrv/check HTTP/1.1" 200 -
2025-08-28 00:04:02,649 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_lfmyz6/check HTTP/1.1" 200 -
2025-08-28 00:04:02,652 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_wc0nqa/check HTTP/1.1" 200 -
2025-08-28 00:04:02,653 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_tv03dd/check HTTP/1.1" 200 -
2025-08-28 00:04:02,656 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_rxwyi4/check HTTP/1.1" 200 -
2025-08-28 00:04:02,663 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_p3d95w/check HTTP/1.1" 200 -
2025-08-28 00:04:02,665 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_p70dr4/check HTTP/1.1" 200 -
2025-08-28 00:04:02,970 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_13bdww/check HTTP/1.1" 200 -
2025-08-28 00:04:02,970 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_1fnr2b/check HTTP/1.1" 200 -
2025-08-28 00:04:02,971 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_7fah8a/check HTTP/1.1" 200 -
2025-08-28 00:04:02,972 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_oxvdvx/check HTTP/1.1" 200 -
2025-08-28 00:04:02,975 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_1swv8v/check HTTP/1.1" 200 -
2025-08-28 00:04:02,978 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_cxel7x/check HTTP/1.1" 200 -
2025-08-28 00:04:03,293 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:03] "GET /api/favorites/hash_f0lhih/check HTTP/1.1" 200 -
2025-08-28 00:04:16,626 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:16,626 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:16,626 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:17,913 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://************:5035
2025-08-28 00:04:17,913 - INFO - [33mPress CTRL+C to quit[0m
2025-08-28 00:04:17,924 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:04:18,420 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:18,420 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:18,421 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:18,434 - WARNING -  * Debugger is active!
2025-08-28 00:04:18,439 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:04:20,386 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "GET / HTTP/1.1" 200 -
2025-08-28 00:04:20,647 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,772 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,780 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,781 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,781 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,825 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:04:21,152 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:04:21,152 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:04:21,153 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:04:21,153 - INFO - 正在获取第0-19条记录...
2025-08-28 00:04:21,153 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:04:21,153 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:04:21,170 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:04:21,170 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:04:21,171 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:04:21,280 - INFO - 响应状态码: 200
2025-08-28 00:04:21,280 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:21,281 - INFO - 响应内容长度: 295817
2025-08-28 00:04:21,281 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:04:21,284 - INFO - 成功解析JSON响应
2025-08-28 00:04:21,284 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:04:21,291 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:21,292 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:21,293 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:21,295 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:21,295 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:21,296 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:21,299 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:21,300 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:21,306 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:21,312 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:21,318 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:21,331 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:21,345 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:21,362 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:21,363 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:21,367 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:21,373 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:21,375 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-28 00:04:21,375 - INFO - 正在获取文档总数...
2025-08-28 00:04:21,375 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:04:21,376 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:04:21,524 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:04:21,524 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:21,524 - INFO - 响应内容长度: 5
2025-08-28 00:04:21,524 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:04:21,524 - INFO - 响应内容前200字符: 39552
2025-08-28 00:04:21,524 - INFO - 使用默认文档总数: 3000
2025-08-28 00:04:21,529 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:21,530 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:21,532 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:21,533 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:21,534 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:21,535 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:21,537 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:21,538 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:21,543 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:21,550 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:21,556 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:21,559 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:21,562 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:21,568 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:21,569 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:21,570 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:21,573 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:21,573 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-28 00:04:21,573 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-28 00:04:21,577 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:04:21,922 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_jvaiga/check HTTP/1.1" 200 -
2025-08-28 00:04:21,922 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_xp372u/check HTTP/1.1" 200 -
2025-08-28 00:04:21,923 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_9yf8ve/check HTTP/1.1" 200 -
2025-08-28 00:04:21,924 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_61jokd/check HTTP/1.1" 200 -
2025-08-28 00:04:21,925 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_ow45rz/check HTTP/1.1" 200 -
2025-08-28 00:04:21,926 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_sthsrv/check HTTP/1.1" 200 -
2025-08-28 00:04:22,242 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_lfmyz6/check HTTP/1.1" 200 -
2025-08-28 00:04:22,243 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_wc0nqa/check HTTP/1.1" 200 -
2025-08-28 00:04:22,243 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_tv03dd/check HTTP/1.1" 200 -
2025-08-28 00:04:22,243 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_rxwyi4/check HTTP/1.1" 200 -
2025-08-28 00:04:22,245 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_p3d95w/check HTTP/1.1" 200 -
2025-08-28 00:04:22,245 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_p70dr4/check HTTP/1.1" 200 -
2025-08-28 00:04:22,564 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_13bdww/check HTTP/1.1" 200 -
2025-08-28 00:04:22,565 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_1fnr2b/check HTTP/1.1" 200 -
2025-08-28 00:04:22,565 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_7fah8a/check HTTP/1.1" 200 -
2025-08-28 00:04:22,566 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_oxvdvx/check HTTP/1.1" 200 -
2025-08-28 00:04:22,567 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_1swv8v/check HTTP/1.1" 200 -
2025-08-28 00:04:22,567 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_cxel7x/check HTTP/1.1" 200 -
2025-08-28 00:04:22,887 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_f0lhih/check HTTP/1.1" 200 -
2025-08-28 00:04:26,861 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:26,861 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,011 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,011 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,012 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,012 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,573 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:04:27,739 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:04:28,025 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:28,025 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:28,026 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:28,041 - WARNING -  * Debugger is active!
2025-08-28 00:04:28,046 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:04:28,167 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:28,168 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:28,168 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:28,176 - WARNING -  * Debugger is active!
2025-08-28 00:04:28,181 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:04:38,754 - INFO - 收到数据请求: {'page': 1, 'limit': 3, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:04:38,755 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-28 00:04:38,755 - INFO - 缓存清理线程已启动
2025-08-28 00:04:38,756 - INFO - 缓存管理器初始化完成
2025-08-28 00:04:38,756 - INFO - 缓存管理器初始化成功
2025-08-28 00:04:38,756 - INFO - 增强搜索请求，参数: page=1, limit=3, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:04:38,756 - INFO - 实时API请求，参数: page=1, limit=3, keyword=''
2025-08-28 00:04:38,756 - INFO - 正在获取第0-2条记录...
2025-08-28 00:04:38,756 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:04:38,756 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '2'}
2025-08-28 00:04:38,908 - INFO - 响应状态码: 200
2025-08-28 00:04:38,908 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:38,908 - INFO - 响应内容长度: 48214
2025-08-28 00:04:38,908 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:04:38,909 - INFO - 成功解析JSON响应
2025-08-28 00:04:38,909 - INFO - 响应是数组格式，包含2条记录
2025-08-28 00:04:38,925 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:38,926 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:38,926 - INFO - 第0-2条记录解析完成，获得2条记录
2025-08-28 00:04:38,926 - INFO - 正在获取文档总数...
2025-08-28 00:04:38,927 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:04:38,927 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:04:39,072 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:04:39,073 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:39,073 - INFO - 响应内容长度: 5
2025-08-28 00:04:39,073 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:04:39,073 - INFO - 响应内容前200字符: 39552
2025-08-28 00:04:39,073 - INFO - 使用默认文档总数: 3000
2025-08-28 00:04:39,079 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:39,081 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:39,081 - INFO - 搜索结果: 原始3000条，筛选后2条
2025-08-28 00:04:39,082 - INFO - 成功返回 2 条OA记录，总计 3000 条
2025-08-28 00:04:39,084 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:39] "GET /api/get_oa_data?page=1&limit=3 HTTP/1.1" 200 -
2025-08-28 00:09:58,513 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "GET / HTTP/1.1" 200 -
2025-08-28 00:09:58,825 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,916 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,925 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,926 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,926 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,975 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:09:59,284 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:09:59,286 - INFO - 使用本地缓存目录: cache
2025-08-28 00:09:59,286 - INFO - 使用本地缓存目录: cache
2025-08-28 00:09:59,286 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:09:59,286 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:09:59,287 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:09:59,287 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:09:59,288 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:09:59,288 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:09:59,288 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:09:59,289 - INFO - 正在获取第0-19条记录...
2025-08-28 00:09:59,289 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:09:59,289 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:09:59,695 - INFO - 响应状态码: 200
2025-08-28 00:09:59,695 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:09:59,696 - INFO - 响应内容长度: 295817
2025-08-28 00:09:59,697 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:09:59,699 - INFO - 成功解析JSON响应
2025-08-28 00:09:59,699 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:09:59,706 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:09:59,707 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:09:59,708 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:09:59,709 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:09:59,710 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:09:59,711 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:09:59,713 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:09:59,714 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:09:59,720 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:09:59,726 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:09:59,731 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:09:59,734 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:09:59,736 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:09:59,742 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:09:59,744 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:09:59,745 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:09:59,749 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:09:59,749 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-28 00:09:59,749 - INFO - 正在获取文档总数...
2025-08-28 00:09:59,749 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:09:59,749 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:09:59,904 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:09:59,904 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:09:59,904 - INFO - 响应内容长度: 5
2025-08-28 00:09:59,905 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:09:59,905 - INFO - 响应内容前200字符: 39552
2025-08-28 00:09:59,905 - INFO - 使用默认文档总数: 3000
2025-08-28 00:09:59,912 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:09:59,914 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:09:59,915 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:09:59,916 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:09:59,917 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:09:59,918 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:09:59,921 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:09:59,921 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:09:59,927 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:09:59,932 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:09:59,939 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:09:59,941 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:09:59,944 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:09:59,953 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:09:59,954 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:09:59,955 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:09:59,959 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:09:59,959 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-28 00:09:59,960 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-28 00:09:59,967 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:10:00,335 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_jvaiga/check HTTP/1.1" 200 -
2025-08-28 00:10:00,336 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_xp372u/check HTTP/1.1" 200 -
2025-08-28 00:10:00,337 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_9yf8ve/check HTTP/1.1" 200 -
2025-08-28 00:10:00,337 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_61jokd/check HTTP/1.1" 200 -
2025-08-28 00:10:00,340 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_ow45rz/check HTTP/1.1" 200 -
2025-08-28 00:10:00,342 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_sthsrv/check HTTP/1.1" 200 -
2025-08-28 00:10:00,658 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_lfmyz6/check HTTP/1.1" 200 -
2025-08-28 00:10:00,658 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_wc0nqa/check HTTP/1.1" 200 -
2025-08-28 00:10:00,659 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_tv03dd/check HTTP/1.1" 200 -
2025-08-28 00:10:00,660 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_rxwyi4/check HTTP/1.1" 200 -
2025-08-28 00:10:00,661 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_p3d95w/check HTTP/1.1" 200 -
2025-08-28 00:10:00,662 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_p70dr4/check HTTP/1.1" 200 -
2025-08-28 00:10:00,981 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_13bdww/check HTTP/1.1" 200 -
2025-08-28 00:10:00,981 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_1fnr2b/check HTTP/1.1" 200 -
2025-08-28 00:10:00,982 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_7fah8a/check HTTP/1.1" 200 -
2025-08-28 00:10:00,982 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_oxvdvx/check HTTP/1.1" 200 -
2025-08-28 00:10:00,983 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_1swv8v/check HTTP/1.1" 200 -
2025-08-28 00:10:00,984 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_cxel7x/check HTTP/1.1" 200 -
2025-08-28 00:10:01,294 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:01] "GET /api/favorites/hash_f0lhih/check HTTP/1.1" 200 -
