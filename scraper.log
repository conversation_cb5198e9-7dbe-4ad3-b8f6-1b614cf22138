2025-08-27 23:56:32,414 - INFO - OA爬虫实例初始化成功
2025-08-27 23:56:32,415 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:56:32,415 - INFO - 请访问: http://localhost:5035
2025-08-27 23:56:33,746 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://************:5035
2025-08-27 23:56:33,746 - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 23:56:33,760 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 23:56:34,075 - INFO - OA爬虫实例初始化成功
2025-08-27 23:56:34,075 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:56:34,075 - INFO - 请访问: http://localhost:5035
2025-08-27 23:56:34,082 - WARNING -  * Debugger is active!
2025-08-27 23:56:34,087 - INFO -  * Debugger PIN: 145-413-362
2025-08-27 23:59:05,981 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,160 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,161 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,161 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:06,457 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 23:59:06,875 - INFO - OA爬虫实例初始化成功
2025-08-27 23:59:06,876 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:59:06,876 - INFO - 请访问: http://localhost:5035
2025-08-27 23:59:06,886 - WARNING -  * Debugger is active!
2025-08-27 23:59:06,892 - INFO -  * Debugger PIN: 145-413-362
2025-08-27 23:59:17,233 - INFO - 收到数据请求: {'page': 1, 'limit': 5, 'keyword': '通知', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-27 23:59:17,233 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-27 23:59:17,237 - INFO - 缓存清理线程已启动
2025-08-27 23:59:17,237 - INFO - 缓存管理器初始化完成
2025-08-27 23:59:17,238 - INFO - 缓存管理器初始化成功
2025-08-27 23:59:17,238 - INFO - 增强搜索请求，参数: page=1, limit=5, keyword='通知', unit='', dept='', start_date='', end_date='', author=''
2025-08-27 23:59:17,238 - INFO - 实时API请求，参数: page=1, limit=5, keyword='通知'
2025-08-27 23:59:17,239 - INFO - 正在获取第0-4条记录...
2025-08-27 23:59:17,239 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-27 23:59:17,239 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '通知', 'row_start': '0', 'row_end': '4'}
2025-08-27 23:59:17,657 - INFO - 响应状态码: 200
2025-08-27 23:59:17,657 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-27 23:59:17,658 - INFO - 响应内容长度: 59168
2025-08-27 23:59:17,658 - INFO - 检测到JSON格式响应，尝试解析
2025-08-27 23:59:17,658 - INFO - 成功解析JSON响应
2025-08-27 23:59:17,658 - INFO - 响应是数组格式，包含4条记录
2025-08-27 23:59:17,675 - ERROR - 解析第0-4条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-27 23:59:17,675 - INFO - 正在获取文档总数...
2025-08-27 23:59:17,675 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-27 23:59:17,675 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '通知'}
2025-08-27 23:59:23,009 - INFO - GetDocNum响应状态码: 200
2025-08-27 23:59:23,010 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-27 23:59:23,010 - INFO - 响应内容长度: 5
2025-08-27 23:59:23,010 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-27 23:59:23,010 - INFO - 响应内容前200字符: 29006
2025-08-27 23:59:23,010 - INFO - 使用默认文档总数: 3000
2025-08-27 23:59:23,010 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-27 23:59:23,011 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-27 23:59:23,012 - INFO - 127.0.0.1 - - [27/Aug/2025 23:59:23] "GET /api/get_oa_data?page=1&limit=5&keyword=通知 HTTP/1.1" 200 -
2025-08-27 23:59:32,465 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:32,465 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:32,469 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:32,478 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\quick_test.py', reloading
2025-08-27 23:59:33,103 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-27 23:59:33,725 - INFO - OA爬虫实例初始化成功
2025-08-27 23:59:33,725 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-27 23:59:33,726 - INFO - 请访问: http://localhost:5035
2025-08-27 23:59:33,737 - WARNING -  * Debugger is active!
2025-08-27 23:59:33,743 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:00:01,088 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:01] "[31m[1mGET / HTTP/1.1[0m" 401 -
2025-08-28 00:00:09,694 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:09] "GET / HTTP/1.1" 200 -
2025-08-28 00:00:10,321 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/css/style.css HTTP/1.1" 200 -
2025-08-28 00:00:10,383 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/favorites-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:10,384 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/cache-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:10,385 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/enhanced-search.js HTTP/1.1" 200 -
2025-08-28 00:00:10,387 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /static/js/text-cleaner-enhanced.js HTTP/1.1" 200 -
2025-08-28 00:00:10,501 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-28 00:00:10,503 - INFO - 缓存清理线程已启动
2025-08-28 00:00:10,503 - INFO - 缓存管理器初始化完成
2025-08-28 00:00:10,504 - INFO - 缓存管理器初始化成功
2025-08-28 00:00:10,504 - INFO - 实时API请求，参数: page=1, limit=200, keyword=''
2025-08-28 00:00:10,504 - INFO - 正在获取第0-199条记录...
2025-08-28 00:00:10,504 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:10,505 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '199'}
2025-08-28 00:00:10,713 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:00:10,714 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:00:10,714 - INFO - 使用本地缓存目录: cache
2025-08-28 00:00:10,714 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:00:10,714 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:00:10,714 - INFO - 正在获取第0-19条记录...
2025-08-28 00:00:10,715 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:00:10,715 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:10,715 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:00:10,728 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:00:10,729 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-28 00:00:10,730 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:10] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:00:11,101 - INFO - 响应状态码: 200
2025-08-28 00:00:11,102 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:11,102 - INFO - 响应内容长度: 295817
2025-08-28 00:00:11,103 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:11,105 - INFO - 成功解析JSON响应
2025-08-28 00:00:11,106 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:00:11,121 - ERROR - 解析第0-19条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:11,121 - INFO - 正在获取文档总数...
2025-08-28 00:00:11,122 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:11,122 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:11,327 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:11,328 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:11,328 - INFO - 响应内容长度: 5
2025-08-28 00:00:11,329 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:11,329 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:11,329 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:11,329 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-28 00:00:11,329 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-28 00:00:11,329 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:11] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:00:12,895 - INFO - 响应状态码: 200
2025-08-28 00:00:12,895 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:12,902 - INFO - 响应内容长度: 8628454
2025-08-28 00:00:12,909 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:12,944 - INFO - 成功解析JSON响应
2025-08-28 00:00:12,945 - INFO - 响应是数组格式，包含199条记录
2025-08-28 00:00:12,949 - ERROR - 解析第0-199条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:12,950 - INFO - 正在获取文档总数...
2025-08-28 00:00:12,950 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:12,950 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:13,192 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:13,192 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:13,192 - INFO - 响应内容长度: 5
2025-08-28 00:00:13,192 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:13,192 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:13,192 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:13,194 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:13] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:00:15,478 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "GET / HTTP/1.1" 200 -
2025-08-28 00:00:15,726 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,837 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,837 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,838 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,838 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:00:15,873 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:15] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:00:16,188 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:00:16,189 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:00:16,189 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:00:16,190 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:00:16,190 - INFO - 正在获取第0-19条记录...
2025-08-28 00:00:16,190 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:16,190 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:00:16,192 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:00:16,194 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:00:16,287 - INFO - 响应状态码: 200
2025-08-28 00:00:16,288 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:16,288 - INFO - 响应内容长度: 295817
2025-08-28 00:00:16,288 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:16,289 - INFO - 成功解析JSON响应
2025-08-28 00:00:16,289 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:00:16,295 - ERROR - 解析第0-19条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:16,295 - INFO - 正在获取文档总数...
2025-08-28 00:00:16,295 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:16,296 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:16,448 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:16,448 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:16,448 - INFO - 响应内容长度: 5
2025-08-28 00:00:16,448 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:16,448 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:16,448 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:16,448 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-28 00:00:16,449 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-28 00:00:16,450 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:16] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:00:19,592 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:00:19,609 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:19] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-08-28 00:00:20,780 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:20] "GET / HTTP/1.1" 200 -
2025-08-28 00:00:21,133 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/css/style.css HTTP/1.1" 200 -
2025-08-28 00:00:21,180 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-08-28 00:00:21,181 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/text-cleaner-enhanced.js HTTP/1.1" 200 -
2025-08-28 00:00:21,181 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/favorites-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:21,182 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/enhanced-search.js HTTP/1.1" 200 -
2025-08-28 00:00:21,182 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:21] "GET /static/js/cache-manager.js HTTP/1.1" 200 -
2025-08-28 00:00:22,689 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:00:22,708 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:00:22,708 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:00:22,709 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:00:22,710 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:00:22,710 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:00:22,711 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:00:22,711 - INFO - 正在获取第0-19条记录...
2025-08-28 00:00:22,711 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:00:22,711 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:00:22,807 - INFO - 响应状态码: 200
2025-08-28 00:00:22,807 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:22,808 - INFO - 响应内容长度: 295817
2025-08-28 00:00:22,808 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:00:22,810 - INFO - 成功解析JSON响应
2025-08-28 00:00:22,810 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:00:22,816 - ERROR - 解析第0-19条记录失败：'OAScraper' object has no attribute 'logger'
2025-08-28 00:00:22,817 - INFO - 正在获取文档总数...
2025-08-28 00:00:22,817 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:00:22,817 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:00:22,970 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:00:22,970 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:00:22,970 - INFO - 响应内容长度: 5
2025-08-28 00:00:22,970 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:00:22,970 - INFO - 响应内容前200字符: 39552
2025-08-28 00:00:22,971 - INFO - 使用默认文档总数: 3000
2025-08-28 00:00:22,971 - INFO - 搜索结果: 原始3000条，筛选后0条
2025-08-28 00:00:22,971 - INFO - 成功返回 0 条OA记录，总计 3000 条
2025-08-28 00:00:22,971 - INFO - 127.0.0.1 - - [28/Aug/2025 00:00:22] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:01:58,001 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\oa_scraper.py', reloading
2025-08-28 00:01:58,003 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\oa_scraper.py', reloading
2025-08-28 00:01:58,146 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:01:58,493 - INFO - OA爬虫实例初始化成功
2025-08-28 00:01:58,493 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:01:58,493 - INFO - 请访问: http://localhost:5035
2025-08-28 00:01:58,504 - WARNING -  * Debugger is active!
2025-08-28 00:01:58,510 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:02:21,978 - INFO - OA爬虫实例初始化成功
2025-08-28 00:02:21,978 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:02:21,979 - INFO - 请访问: http://localhost:5035
2025-08-28 00:02:23,269 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://************:5035
2025-08-28 00:02:23,269 - INFO - [33mPress CTRL+C to quit[0m
2025-08-28 00:02:23,275 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:02:23,744 - INFO - OA爬虫实例初始化成功
2025-08-28 00:02:23,744 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:02:23,745 - INFO - 请访问: http://localhost:5035
2025-08-28 00:02:23,758 - WARNING -  * Debugger is active!
2025-08-28 00:02:23,761 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:02:43,273 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,354 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,355 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,355 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\test_fix.py', reloading
2025-08-28 00:02:43,888 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:02:44,308 - INFO - OA爬虫实例初始化成功
2025-08-28 00:02:44,308 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:02:44,308 - INFO - 请访问: http://localhost:5035
2025-08-28 00:02:44,324 - WARNING -  * Debugger is active!
2025-08-28 00:02:44,327 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:02:54,253 - INFO - 收到数据请求: {'page': 1, 'limit': 5, 'keyword': '通知', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:02:54,253 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-28 00:02:54,254 - INFO - 缓存清理线程已启动
2025-08-28 00:02:54,255 - INFO - 缓存管理器初始化完成
2025-08-28 00:02:54,255 - INFO - 缓存管理器初始化成功
2025-08-28 00:02:54,255 - INFO - 从缓存返回 0 条OA记录
2025-08-28 00:02:54,255 - INFO - 127.0.0.1 - - [28/Aug/2025 00:02:54] "GET /api/get_oa_data?page=1&limit=5&keyword=通知 HTTP/1.1" 200 -
2025-08-28 00:04:00,500 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "GET / HTTP/1.1" 200 -
2025-08-28 00:04:00,808 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,908 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,908 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,909 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,909 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:00,948 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:00] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:04:01,272 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:04:01,273 - INFO - 使用本地缓存目录: cache
2025-08-28 00:04:01,274 - INFO - 使用本地缓存目录: cache
2025-08-28 00:04:01,274 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:04:01,274 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:04:01,274 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:04:01,274 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:04:01,275 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:04:01,275 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:04:01,275 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:04:01,275 - INFO - 正在获取第0-19条记录...
2025-08-28 00:04:01,275 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:04:01,276 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:04:01,642 - INFO - 响应状态码: 200
2025-08-28 00:04:01,648 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:01,650 - INFO - 响应内容长度: 295817
2025-08-28 00:04:01,651 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:04:01,653 - INFO - 成功解析JSON响应
2025-08-28 00:04:01,656 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:04:01,676 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:01,680 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:01,685 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:01,689 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:01,692 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:01,694 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:01,697 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:01,701 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:01,709 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:01,716 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:01,723 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:01,726 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:01,729 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:01,736 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:01,737 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:01,737 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:01,740 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:01,741 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-28 00:04:01,741 - INFO - 正在获取文档总数...
2025-08-28 00:04:01,742 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:04:01,742 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:04:01,929 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:04:01,929 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:01,929 - INFO - 响应内容长度: 5
2025-08-28 00:04:01,930 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:04:01,930 - INFO - 响应内容前200字符: 39552
2025-08-28 00:04:01,930 - INFO - 使用默认文档总数: 3000
2025-08-28 00:04:01,937 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:01,939 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:01,940 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:01,942 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:01,942 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:01,943 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:01,946 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:01,947 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:01,952 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:01,959 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:01,966 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:01,969 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:01,972 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:01,978 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:01,979 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:01,980 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:01,983 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:01,983 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-28 00:04:01,983 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-28 00:04:01,990 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:01] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:04:02,326 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_jvaiga/check HTTP/1.1" 200 -
2025-08-28 00:04:02,329 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_xp372u/check HTTP/1.1" 200 -
2025-08-28 00:04:02,336 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_9yf8ve/check HTTP/1.1" 200 -
2025-08-28 00:04:02,342 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_61jokd/check HTTP/1.1" 200 -
2025-08-28 00:04:02,344 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_ow45rz/check HTTP/1.1" 200 -
2025-08-28 00:04:02,351 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_sthsrv/check HTTP/1.1" 200 -
2025-08-28 00:04:02,649 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_lfmyz6/check HTTP/1.1" 200 -
2025-08-28 00:04:02,652 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_wc0nqa/check HTTP/1.1" 200 -
2025-08-28 00:04:02,653 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_tv03dd/check HTTP/1.1" 200 -
2025-08-28 00:04:02,656 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_rxwyi4/check HTTP/1.1" 200 -
2025-08-28 00:04:02,663 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_p3d95w/check HTTP/1.1" 200 -
2025-08-28 00:04:02,665 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_p70dr4/check HTTP/1.1" 200 -
2025-08-28 00:04:02,970 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_13bdww/check HTTP/1.1" 200 -
2025-08-28 00:04:02,970 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_1fnr2b/check HTTP/1.1" 200 -
2025-08-28 00:04:02,971 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_7fah8a/check HTTP/1.1" 200 -
2025-08-28 00:04:02,972 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_oxvdvx/check HTTP/1.1" 200 -
2025-08-28 00:04:02,975 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_1swv8v/check HTTP/1.1" 200 -
2025-08-28 00:04:02,978 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:02] "GET /api/favorites/hash_cxel7x/check HTTP/1.1" 200 -
2025-08-28 00:04:03,293 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:03] "GET /api/favorites/hash_f0lhih/check HTTP/1.1" 200 -
2025-08-28 00:04:16,626 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:16,626 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:16,626 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:17,913 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://************:5035
2025-08-28 00:04:17,913 - INFO - [33mPress CTRL+C to quit[0m
2025-08-28 00:04:17,924 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:04:18,420 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:18,420 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:18,421 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:18,434 - WARNING -  * Debugger is active!
2025-08-28 00:04:18,439 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:04:20,386 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "GET / HTTP/1.1" 200 -
2025-08-28 00:04:20,647 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,772 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,780 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,781 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,781 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:04:20,825 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:20] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:04:21,152 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:04:21,152 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:04:21,153 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:04:21,153 - INFO - 正在获取第0-19条记录...
2025-08-28 00:04:21,153 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:04:21,153 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:04:21,170 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:04:21,170 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:04:21,171 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:04:21,280 - INFO - 响应状态码: 200
2025-08-28 00:04:21,280 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:21,281 - INFO - 响应内容长度: 295817
2025-08-28 00:04:21,281 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:04:21,284 - INFO - 成功解析JSON响应
2025-08-28 00:04:21,284 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:04:21,291 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:21,292 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:21,293 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:21,295 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:21,295 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:21,296 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:21,299 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:21,300 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:21,306 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:21,312 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:21,318 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:21,331 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:21,345 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:21,362 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:21,363 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:21,367 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:21,373 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:21,375 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-28 00:04:21,375 - INFO - 正在获取文档总数...
2025-08-28 00:04:21,375 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:04:21,376 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:04:21,524 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:04:21,524 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:21,524 - INFO - 响应内容长度: 5
2025-08-28 00:04:21,524 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:04:21,524 - INFO - 响应内容前200字符: 39552
2025-08-28 00:04:21,524 - INFO - 使用默认文档总数: 3000
2025-08-28 00:04:21,529 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:21,530 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:21,532 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:04:21,533 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:04:21,534 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:04:21,535 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:04:21,537 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:04:21,538 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:04:21,543 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:04:21,550 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:04:21,556 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:04:21,559 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:04:21,562 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:04:21,568 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:04:21,569 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:04:21,570 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:04:21,573 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:04:21,573 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-28 00:04:21,573 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-28 00:04:21,577 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:04:21,922 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_jvaiga/check HTTP/1.1" 200 -
2025-08-28 00:04:21,922 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_xp372u/check HTTP/1.1" 200 -
2025-08-28 00:04:21,923 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_9yf8ve/check HTTP/1.1" 200 -
2025-08-28 00:04:21,924 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_61jokd/check HTTP/1.1" 200 -
2025-08-28 00:04:21,925 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_ow45rz/check HTTP/1.1" 200 -
2025-08-28 00:04:21,926 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:21] "GET /api/favorites/hash_sthsrv/check HTTP/1.1" 200 -
2025-08-28 00:04:22,242 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_lfmyz6/check HTTP/1.1" 200 -
2025-08-28 00:04:22,243 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_wc0nqa/check HTTP/1.1" 200 -
2025-08-28 00:04:22,243 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_tv03dd/check HTTP/1.1" 200 -
2025-08-28 00:04:22,243 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_rxwyi4/check HTTP/1.1" 200 -
2025-08-28 00:04:22,245 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_p3d95w/check HTTP/1.1" 200 -
2025-08-28 00:04:22,245 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_p70dr4/check HTTP/1.1" 200 -
2025-08-28 00:04:22,564 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_13bdww/check HTTP/1.1" 200 -
2025-08-28 00:04:22,565 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_1fnr2b/check HTTP/1.1" 200 -
2025-08-28 00:04:22,565 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_7fah8a/check HTTP/1.1" 200 -
2025-08-28 00:04:22,566 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_oxvdvx/check HTTP/1.1" 200 -
2025-08-28 00:04:22,567 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_1swv8v/check HTTP/1.1" 200 -
2025-08-28 00:04:22,567 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_cxel7x/check HTTP/1.1" 200 -
2025-08-28 00:04:22,887 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:22] "GET /api/favorites/hash_f0lhih/check HTTP/1.1" 200 -
2025-08-28 00:04:26,861 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:26,861 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,011 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,011 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,012 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,012 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\simple_test.py', reloading
2025-08-28 00:04:27,573 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:04:27,739 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-28 00:04:28,025 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:28,025 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:28,026 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:28,041 - WARNING -  * Debugger is active!
2025-08-28 00:04:28,046 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:04:28,167 - INFO - OA爬虫实例初始化成功
2025-08-28 00:04:28,168 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-28 00:04:28,168 - INFO - 请访问: http://localhost:5035
2025-08-28 00:04:28,176 - WARNING -  * Debugger is active!
2025-08-28 00:04:28,181 - INFO -  * Debugger PIN: 145-413-362
2025-08-28 00:04:38,754 - INFO - 收到数据请求: {'page': 1, 'limit': 3, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:04:38,755 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-28 00:04:38,755 - INFO - 缓存清理线程已启动
2025-08-28 00:04:38,756 - INFO - 缓存管理器初始化完成
2025-08-28 00:04:38,756 - INFO - 缓存管理器初始化成功
2025-08-28 00:04:38,756 - INFO - 增强搜索请求，参数: page=1, limit=3, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:04:38,756 - INFO - 实时API请求，参数: page=1, limit=3, keyword=''
2025-08-28 00:04:38,756 - INFO - 正在获取第0-2条记录...
2025-08-28 00:04:38,756 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:04:38,756 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '2'}
2025-08-28 00:04:38,908 - INFO - 响应状态码: 200
2025-08-28 00:04:38,908 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:38,908 - INFO - 响应内容长度: 48214
2025-08-28 00:04:38,908 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:04:38,909 - INFO - 成功解析JSON响应
2025-08-28 00:04:38,909 - INFO - 响应是数组格式，包含2条记录
2025-08-28 00:04:38,925 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:38,926 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:38,926 - INFO - 第0-2条记录解析完成，获得2条记录
2025-08-28 00:04:38,926 - INFO - 正在获取文档总数...
2025-08-28 00:04:38,927 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:04:38,927 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:04:39,072 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:04:39,073 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:04:39,073 - INFO - 响应内容长度: 5
2025-08-28 00:04:39,073 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:04:39,073 - INFO - 响应内容前200字符: 39552
2025-08-28 00:04:39,073 - INFO - 使用默认文档总数: 3000
2025-08-28 00:04:39,079 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:04:39,081 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:04:39,081 - INFO - 搜索结果: 原始3000条，筛选后2条
2025-08-28 00:04:39,082 - INFO - 成功返回 2 条OA记录，总计 3000 条
2025-08-28 00:04:39,084 - INFO - 127.0.0.1 - - [28/Aug/2025 00:04:39] "GET /api/get_oa_data?page=1&limit=3 HTTP/1.1" 200 -
2025-08-28 00:09:58,513 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "GET / HTTP/1.1" 200 -
2025-08-28 00:09:58,825 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,916 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/text-cleaner-enhanced.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,925 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/cache-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,926 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/enhanced-search.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,926 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "[36mGET /static/js/favorites-manager.js HTTP/1.1[0m" 304 -
2025-08-28 00:09:58,975 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:58] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-28 00:09:59,284 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-28 00:09:59,286 - INFO - 使用本地缓存目录: cache
2025-08-28 00:09:59,286 - INFO - 使用本地缓存目录: cache
2025-08-28 00:09:59,286 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:09:59,286 - INFO - 收藏夹管理器初始化成功
2025-08-28 00:09:59,287 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/favorites?page=1&limit=20 HTTP/1.1" 200 -
2025-08-28 00:09:59,287 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/favorites/tags HTTP/1.1" 200 -
2025-08-28 00:09:59,288 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/cache/stats HTTP/1.1" 200 -
2025-08-28 00:09:59,288 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-28 00:09:59,288 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-28 00:09:59,289 - INFO - 正在获取第0-19条记录...
2025-08-28 00:09:59,289 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-28 00:09:59,289 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-28 00:09:59,695 - INFO - 响应状态码: 200
2025-08-28 00:09:59,695 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:09:59,696 - INFO - 响应内容长度: 295817
2025-08-28 00:09:59,697 - INFO - 检测到JSON格式响应，尝试解析
2025-08-28 00:09:59,699 - INFO - 成功解析JSON响应
2025-08-28 00:09:59,699 - INFO - 响应是数组格式，包含19条记录
2025-08-28 00:09:59,706 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:09:59,707 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:09:59,708 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:09:59,709 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:09:59,710 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:09:59,711 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:09:59,713 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:09:59,714 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:09:59,720 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:09:59,726 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:09:59,731 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:09:59,734 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:09:59,736 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:09:59,742 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:09:59,744 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:09:59,745 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:09:59,749 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:09:59,749 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-28 00:09:59,749 - INFO - 正在获取文档总数...
2025-08-28 00:09:59,749 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-28 00:09:59,749 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-28 00:09:59,904 - INFO - GetDocNum响应状态码: 200
2025-08-28 00:09:59,904 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-28 00:09:59,904 - INFO - 响应内容长度: 5
2025-08-28 00:09:59,905 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-28 00:09:59,905 - INFO - 响应内容前200字符: 39552
2025-08-28 00:09:59,905 - INFO - 使用默认文档总数: 3000
2025-08-28 00:09:59,912 - INFO - 文本清理：原始长度 39614，清理后长度 39118
2025-08-28 00:09:59,914 - INFO - 文本清理：原始长度 6166，清理后长度 5947
2025-08-28 00:09:59,915 - INFO - 文本清理：原始长度 5005，清理后长度 4865
2025-08-28 00:09:59,916 - INFO - 文本清理：原始长度 4857，清理后长度 4766
2025-08-28 00:09:59,917 - INFO - 文本清理：原始长度 1537，清理后长度 1445
2025-08-28 00:09:59,918 - INFO - 文本清理：原始长度 5120，清理后长度 5002
2025-08-28 00:09:59,921 - INFO - 文本清理：原始长度 14023，清理后长度 13703
2025-08-28 00:09:59,921 - INFO - 文本清理：原始长度 2736，清理后长度 2680
2025-08-28 00:09:59,927 - INFO - 文本清理：原始长度 39116，清理后长度 38139
2025-08-28 00:09:59,932 - INFO - 文本清理：原始长度 30672，清理后长度 29143
2025-08-28 00:09:59,939 - INFO - 文本清理：原始长度 36472，清理后长度 34931
2025-08-28 00:09:59,941 - INFO - 文本清理：原始长度 12952，清理后长度 12071
2025-08-28 00:09:59,944 - INFO - 文本清理：原始长度 3999，清理后长度 3474
2025-08-28 00:09:59,953 - INFO - 文本清理：原始长度 48805，清理后长度 48521
2025-08-28 00:09:59,954 - INFO - 文本清理：原始长度 1339，清理后长度 1142
2025-08-28 00:09:59,955 - INFO - 文本清理：原始长度 1975，清理后长度 1858
2025-08-28 00:09:59,959 - INFO - 文本清理：原始长度 15702，清理后长度 15353
2025-08-28 00:09:59,959 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-28 00:09:59,960 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-28 00:09:59,967 - INFO - 127.0.0.1 - - [28/Aug/2025 00:09:59] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-28 00:10:00,335 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_jvaiga/check HTTP/1.1" 200 -
2025-08-28 00:10:00,336 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_xp372u/check HTTP/1.1" 200 -
2025-08-28 00:10:00,337 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_9yf8ve/check HTTP/1.1" 200 -
2025-08-28 00:10:00,337 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_61jokd/check HTTP/1.1" 200 -
2025-08-28 00:10:00,340 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_ow45rz/check HTTP/1.1" 200 -
2025-08-28 00:10:00,342 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_sthsrv/check HTTP/1.1" 200 -
2025-08-28 00:10:00,658 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_lfmyz6/check HTTP/1.1" 200 -
2025-08-28 00:10:00,658 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_wc0nqa/check HTTP/1.1" 200 -
2025-08-28 00:10:00,659 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_tv03dd/check HTTP/1.1" 200 -
2025-08-28 00:10:00,660 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_rxwyi4/check HTTP/1.1" 200 -
2025-08-28 00:10:00,661 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_p3d95w/check HTTP/1.1" 200 -
2025-08-28 00:10:00,662 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_p70dr4/check HTTP/1.1" 200 -
2025-08-28 00:10:00,981 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_13bdww/check HTTP/1.1" 200 -
2025-08-28 00:10:00,981 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_1fnr2b/check HTTP/1.1" 200 -
2025-08-28 00:10:00,982 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_7fah8a/check HTTP/1.1" 200 -
2025-08-28 00:10:00,982 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_oxvdvx/check HTTP/1.1" 200 -
2025-08-28 00:10:00,983 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_1swv8v/check HTTP/1.1" 200 -
2025-08-28 00:10:00,984 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:00] "GET /api/favorites/hash_cxel7x/check HTTP/1.1" 200 -
2025-08-28 00:10:01,294 - INFO - 127.0.0.1 - - [28/Aug/2025 00:10:01] "GET /api/favorites/hash_f0lhih/check HTTP/1.1" 200 -
2025-08-30 20:44:57,279 - INFO - OA爬虫实例初始化成功
2025-08-30 20:44:57,281 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-30 20:44:57,281 - INFO - 请访问: http://localhost:5035
2025-08-30 20:44:58,589 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5035
 * Running on http://**********:5035
2025-08-30 20:44:58,589 - INFO - [33mPress CTRL+C to quit[0m
2025-08-30 20:44:58,600 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-30 20:44:58,873 - INFO - OA爬虫实例初始化成功
2025-08-30 20:44:58,873 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-30 20:44:58,873 - INFO - 请访问: http://localhost:5035
2025-08-30 20:44:58,882 - WARNING -  * Debugger is active!
2025-08-30 20:44:58,886 - INFO -  * Debugger PIN: 145-413-362
2025-08-30 20:45:30,913 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-30 20:45:30,915 - INFO - 缓存清理线程已启动
2025-08-30 20:45:30,915 - INFO - 缓存管理器初始化完成
2025-08-30 20:45:30,915 - INFO - 缓存管理器初始化成功
2025-08-30 20:45:30,916 - INFO - 实时API请求，参数: page=1, limit=200, keyword=''
2025-08-30 20:45:30,917 - INFO - 正在获取第0-199条记录...
2025-08-30 20:45:30,917 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-30 20:45:30,917 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '199'}
2025-08-30 20:45:32,978 - INFO - 响应状态码: 200
2025-08-30 20:45:32,978 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-30 20:45:32,987 - INFO - 响应内容长度: 8766241
2025-08-30 20:45:32,993 - INFO - 检测到JSON格式响应，尝试解析
2025-08-30 20:45:33,029 - INFO - 成功解析JSON响应
2025-08-30 20:45:33,030 - INFO - 响应是数组格式，包含199条记录
2025-08-30 20:45:33,088 - INFO - 文本清理：原始长度 142449，清理后长度 139053
2025-08-30 20:45:33,091 - INFO - 文本清理：原始长度 7368，清理后长度 7310
2025-08-30 20:45:33,092 - INFO - 文本清理：原始长度 4525，清理后长度 4462
2025-08-30 20:45:33,093 - INFO - 文本清理：原始长度 1500，清理后长度 746
2025-08-30 20:45:33,093 - INFO - 文本清理：原始长度 2353，清理后长度 1187
2025-08-30 20:45:33,095 - INFO - 文本清理：原始长度 8907，清理后长度 8867
2025-08-30 20:45:33,099 - INFO - 文本清理：原始长度 12838，清理后长度 12760
2025-08-30 20:45:33,106 - INFO - 文本清理：原始长度 39614，清理后长度 39185
2025-08-30 20:45:33,109 - INFO - 文本清理：原始长度 6166，清理后长度 6085
2025-08-30 20:45:33,110 - INFO - 文本清理：原始长度 5005，清理后长度 4946
2025-08-30 20:45:33,112 - INFO - 文本清理：原始长度 4857，清理后长度 1725
2025-08-30 20:45:33,112 - INFO - 文本清理：原始长度 1537，清理后长度 806
2025-08-30 20:45:33,122 - INFO - 文本清理：原始长度 43547，清理后长度 42707
2025-08-30 20:45:33,124 - INFO - 文本清理：原始长度 5120，清理后长度 5047
2025-08-30 20:45:33,128 - INFO - 文本清理：原始长度 14023，清理后长度 4435
2025-08-30 20:45:33,129 - INFO - 文本清理：原始长度 2736，清理后长度 1183
2025-08-30 20:45:33,139 - INFO - 文本清理：原始长度 39116，清理后长度 38200
2025-08-30 20:45:33,149 - INFO - 文本清理：原始长度 30672，清理后长度 30507
2025-08-30 20:45:33,156 - INFO - 文本清理：原始长度 36472，清理后长度 36273
2025-08-30 20:45:33,157 - INFO - 文本清理：原始长度 3725，清理后长度 1263
2025-08-30 20:45:33,160 - INFO - 文本清理：原始长度 12952，清理后长度 12888
2025-08-30 20:45:33,191 - INFO - 文本清理：原始长度 94623，清理后长度 92219
2025-08-30 20:45:33,193 - INFO - 文本清理：原始长度 5620，清理后长度 2365
2025-08-30 20:45:33,194 - INFO - 文本清理：原始长度 3999，清理后长度 1735
2025-08-30 20:45:33,214 - INFO - 文本清理：原始长度 48805，清理后长度 47315
2025-08-30 20:45:33,215 - INFO - 文本清理：原始长度 1339，清理后长度 582
2025-08-30 20:45:33,216 - INFO - 文本清理：原始长度 1975，清理后长度 1229
2025-08-30 20:45:33,220 - INFO - 文本清理：原始长度 15702，清理后长度 15447
2025-08-30 20:45:33,222 - INFO - 文本清理：原始长度 4073，清理后长度 4009
2025-08-30 20:45:33,242 - INFO - 文本清理：原始长度 106215，清理后长度 99976
2025-08-30 20:45:33,333 - INFO - 文本清理：原始长度 363372，清理后长度 286515
2025-08-30 20:45:33,338 - INFO - 文本清理：原始长度 27440，清理后长度 27191
2025-08-30 20:45:33,339 - INFO - 文本清理：原始长度 2735，清理后长度 865
2025-08-30 20:45:33,340 - INFO - 文本清理：原始长度 2945，清理后长度 902
2025-08-30 20:45:33,348 - INFO - 文本清理：原始长度 36864，清理后长度 35734
2025-08-30 20:45:33,350 - INFO - 文本清理：原始长度 3235，清理后长度 3195
2025-08-30 20:45:33,351 - INFO - 文本清理：原始长度 5023，清理后长度 4937
2025-08-30 20:45:33,352 - INFO - 文本清理：原始长度 5414，清理后长度 5383
2025-08-30 20:45:33,376 - INFO - 文本清理：原始长度 76615，清理后长度 71996
2025-08-30 20:45:33,393 - INFO - 文本清理：原始长度 66748，清理后长度 65150
2025-08-30 20:45:33,397 - INFO - 文本清理：原始长度 20348，清理后长度 19501
2025-08-30 20:45:33,401 - INFO - 文本清理：原始长度 11718，清理后长度 11616
2025-08-30 20:45:33,402 - INFO - 文本清理：原始长度 3445，清理后长度 1189
2025-08-30 20:45:33,405 - INFO - 文本清理：原始长度 12438，清理后长度 12327
2025-08-30 20:45:33,410 - INFO - 文本清理：原始长度 21002，清理后长度 9028
2025-08-30 20:45:33,416 - INFO - 文本清理：原始长度 20084，清理后长度 6323
2025-08-30 20:45:33,422 - INFO - 文本清理：原始长度 22300，清理后长度 22073
2025-08-30 20:45:33,446 - INFO - 文本清理：原始长度 75578，清理后长度 72462
2025-08-30 20:45:33,447 - INFO - 文本清理：原始长度 120，清理后长度 99
2025-08-30 20:45:33,448 - INFO - 文本清理：原始长度 2115，清理后长度 944
2025-08-30 20:45:33,449 - INFO - 文本清理：原始长度 6071，清理后长度 2089
2025-08-30 20:45:33,454 - INFO - 文本清理：原始长度 22139，清理后长度 21915
2025-08-30 20:45:33,462 - INFO - 文本清理：原始长度 38301，清理后长度 36904
2025-08-30 20:45:33,463 - INFO - 文本清理：原始长度 3882，清理后长度 3843
2025-08-30 20:45:33,464 - INFO - 文本清理：原始长度 2933，清理后长度 2903
2025-08-30 20:45:33,474 - INFO - 文本清理：原始长度 39385，清理后长度 37167
2025-08-30 20:45:33,475 - INFO - 文本清理：原始长度 3450，清理后长度 3410
2025-08-30 20:45:33,477 - INFO - 文本清理：原始长度 3890，清理后长度 1192
2025-08-30 20:45:33,485 - INFO - 文本清理：原始长度 32457，清理后长度 31763
2025-08-30 20:45:33,488 - INFO - 文本清理：原始长度 8500，清理后长度 8391
2025-08-30 20:45:33,493 - INFO - 文本清理：原始长度 18594，清理后长度 6299
2025-08-30 20:45:33,504 - INFO - 文本清理：原始长度 30271，清理后长度 12355
2025-08-30 20:45:33,505 - INFO - 文本清理：原始长度 2704，清理后长度 2651
2025-08-30 20:45:33,523 - INFO - 文本清理：原始长度 53831，清理后长度 51531
2025-08-30 20:45:33,608 - INFO - 文本清理：原始长度 314384，清理后长度 104068
2025-08-30 20:45:33,611 - INFO - 文本清理：原始长度 12505，清理后长度 12423
2025-08-30 20:45:33,612 - INFO - 文本清理：原始长度 2074，清理后长度 1005
2025-08-30 20:45:33,613 - INFO - 文本清理：原始长度 1072，清理后长度 699
2025-08-30 20:45:33,626 - INFO - 文本清理：原始长度 41059，清理后长度 39880
2025-08-30 20:45:33,628 - INFO - 文本清理：原始长度 3406，清理后长度 3357
2025-08-30 20:45:33,650 - INFO - 文本清理：原始长度 101346，清理后长度 98116
2025-08-30 20:45:33,653 - INFO - 文本清理：原始长度 8807，清理后长度 8736
2025-08-30 20:45:33,654 - INFO - 文本清理：原始长度 5024，清理后长度 4948
2025-08-30 20:45:33,679 - INFO - 文本清理：原始长度 98874，清理后长度 95645
2025-08-30 20:45:33,680 - INFO - 文本清理：原始长度 3718，清理后长度 1420
2025-08-30 20:45:33,709 - INFO - 文本清理：原始长度 96546，清理后长度 93338
2025-08-30 20:45:33,711 - INFO - 文本清理：原始长度 7707，清理后长度 7644
2025-08-30 20:45:33,718 - INFO - 文本清理：原始长度 29471，清理后长度 29332
2025-08-30 20:45:33,727 - INFO - 文本清理：原始长度 39873，清理后长度 37970
2025-08-30 20:45:33,728 - INFO - 文本清理：原始长度 2865，清理后长度 2834
2025-08-30 20:45:33,729 - INFO - 文本清理：原始长度 1549，清理后长度 899
2025-08-30 20:45:33,769 - INFO - 文本清理：原始长度 150589，清理后长度 147024
2025-08-30 20:45:33,770 - INFO - 文本清理：原始长度 2534，清理后长度 1004
2025-08-30 20:45:33,790 - INFO - 文本清理：原始长度 67283，清理后长度 52589
2025-08-30 20:45:33,807 - INFO - 文本清理：原始长度 49291，清理后长度 25992
2025-08-30 20:45:33,809 - INFO - 文本清理：原始长度 5505，清理后长度 2002
2025-08-30 20:45:33,813 - INFO - 文本清理：原始长度 12445，清理后长度 12238
2025-08-30 20:45:33,815 - INFO - 文本清理：原始长度 3661，清理后长度 3613
2025-08-30 20:45:33,845 - INFO - 文本清理：原始长度 97844，清理后长度 95443
2025-08-30 20:45:33,846 - INFO - 文本清理：原始长度 2432，清理后长度 2401
2025-08-30 20:45:33,847 - INFO - 文本清理：原始长度 5247，清理后长度 5216
2025-08-30 20:45:33,861 - INFO - 文本清理：原始长度 61551，清理后长度 59706
2025-08-30 20:45:33,870 - INFO - 文本清理：原始长度 35568，清理后长度 34722
2025-08-30 20:45:33,870 - INFO - 文本清理：原始长度 1874，清理后长度 583
2025-08-30 20:45:33,872 - INFO - 文本清理：原始长度 4711，清理后长度 4659
2025-08-30 20:45:33,873 - INFO - 文本清理：原始长度 4268，清理后长度 4213
2025-08-30 20:45:33,874 - INFO - 文本清理：原始长度 4100，清理后长度 4058
2025-08-30 20:45:33,875 - INFO - 文本清理：原始长度 4087，清理后长度 4048
2025-08-30 20:45:33,876 - INFO - 文本清理：原始长度 4969，清理后长度 1841
2025-08-30 20:45:33,900 - INFO - 文本清理：原始长度 169660，清理后长度 161280
2025-08-30 20:45:33,906 - INFO - 文本清理：原始长度 37182，清理后长度 35370
2025-08-30 20:45:33,909 - INFO - 文本清理：原始长度 5094，清理后长度 5035
2025-08-30 20:45:33,987 - INFO - 文本清理：原始长度 266301，清理后长度 193375
2025-08-30 20:45:34,370 - INFO - 文本清理：原始长度 1150429，清理后长度 1134503
2025-08-30 20:45:34,372 - INFO - 文本清理：原始长度 2939，清理后长度 1269
2025-08-30 20:45:34,395 - INFO - 文本清理：原始长度 97583，清理后长度 94363
2025-08-30 20:45:34,428 - INFO - 文本清理：原始长度 98267，清理后长度 95049
2025-08-30 20:45:34,429 - INFO - 文本清理：原始长度 4649，清理后长度 4565
2025-08-30 20:45:34,430 - INFO - 文本清理：原始长度 1022，清理后长度 526
2025-08-30 20:45:34,441 - INFO - 文本清理：原始长度 34057，清理后长度 32962
2025-08-30 20:45:34,505 - INFO - 文本清理：原始长度 160537，清理后长度 152244
2025-08-30 20:45:34,508 - INFO - 文本清理：原始长度 7988，清理后长度 2680
2025-08-30 20:45:34,511 - INFO - 文本清理：原始长度 14159，清理后长度 14041
2025-08-30 20:45:34,516 - INFO - 文本清理：原始长度 11166，清理后长度 11069
2025-08-30 20:45:34,528 - INFO - 文本清理：原始长度 33105，清理后长度 32005
2025-08-30 20:45:34,540 - INFO - 文本清理：原始长度 44694，清理后长度 42438
2025-08-30 20:45:34,542 - INFO - 文本清理：原始长度 4191，清理后长度 4160
2025-08-30 20:45:34,543 - INFO - 文本清理：原始长度 5148，清理后长度 5114
2025-08-30 20:45:34,556 - INFO - 文本清理：原始长度 39810，清理后长度 38680
2025-08-30 20:45:34,567 - INFO - 文本清理：原始长度 32276，清理后长度 31198
2025-08-30 20:45:34,570 - INFO - 文本清理：原始长度 6264，清理后长度 6178
2025-08-30 20:45:34,595 - INFO - 文本清理：原始长度 105037，清理后长度 102603
2025-08-30 20:45:34,622 - INFO - 文本清理：原始长度 101773，清理后长度 98520
2025-08-30 20:45:34,625 - INFO - 文本清理：原始长度 5059，清理后长度 5019
2025-08-30 20:45:34,633 - INFO - 文本清理：原始长度 15778，清理后长度 15645
2025-08-30 20:45:34,636 - INFO - 文本清理：原始长度 7159，清理后长度 7085
2025-08-30 20:45:34,638 - INFO - 文本清理：原始长度 1782，清理后长度 1137
2025-08-30 20:45:34,641 - INFO - 文本清理：原始长度 4879，清理后长度 2980
2025-08-30 20:45:34,649 - INFO - 文本清理：原始长度 17328，清理后长度 17255
2025-08-30 20:45:34,653 - INFO - 文本清理：原始长度 8243，清理后长度 8200
2025-08-30 20:45:34,680 - INFO - 文本清理：原始长度 38388，清理后长度 36200
2025-08-30 20:45:34,687 - INFO - 文本清理：原始长度 3548，清理后长度 1249
2025-08-30 20:45:34,691 - INFO - 文本清理：原始长度 11580，清理后长度 11480
2025-08-30 20:45:34,737 - INFO - 文本清理：原始长度 75077，清理后长度 66558
2025-08-30 20:45:34,741 - INFO - 文本清理：原始长度 15054，清理后长度 14883
2025-08-30 20:45:34,743 - INFO - 文本清理：原始长度 5096，清理后长度 1889
2025-08-30 20:45:34,757 - INFO - 文本清理：原始长度 39907，清理后长度 39046
2025-08-30 20:45:34,766 - INFO - 文本清理：原始长度 26713，清理后长度 26508
2025-08-30 20:45:34,783 - INFO - 文本清理：原始长度 42290，清理后长度 41091
2025-08-30 20:45:34,816 - INFO - 文本清理：原始长度 96279，清理后长度 93076
2025-08-30 20:45:34,842 - INFO - 文本清理：原始长度 96363，清理后长度 93157
2025-08-30 20:45:34,852 - INFO - 文本清理：原始长度 48306，清理后长度 46727
2025-08-30 20:45:34,867 - INFO - 文本清理：原始长度 39108，清理后长度 38292
2025-08-30 20:45:34,877 - INFO - 文本清理：原始长度 32320，清理后长度 31243
2025-08-30 20:45:34,878 - INFO - 文本清理：原始长度 5627，清理后长度 5572
2025-08-30 20:45:34,888 - INFO - 文本清理：原始长度 33695，清理后长度 32602
2025-08-30 20:45:34,905 - INFO - 文本清理：原始长度 70677，清理后长度 68448
2025-08-30 20:45:34,907 - INFO - 文本清理：原始长度 5964，清理后长度 5935
2025-08-30 20:45:34,919 - INFO - 文本清理：原始长度 51039，清理后长度 50106
2025-08-30 20:45:34,932 - INFO - 文本清理：原始长度 52277，清理后长度 51430
2025-08-30 20:45:34,940 - INFO - 文本清理：原始长度 36851，清理后长度 35738
2025-08-30 20:45:34,941 - INFO - 文本清理：原始长度 2900，清理后长度 2866
2025-08-30 20:45:34,943 - INFO - 文本清理：原始长度 8554，清理后长度 8511
2025-08-30 20:45:34,947 - INFO - 文本清理：原始长度 18559，清理后长度 18423
2025-08-30 20:45:34,968 - INFO - 文本清理：原始长度 78037，清理后长度 76204
2025-08-30 20:45:34,971 - INFO - 文本清理：原始长度 7685，清理后长度 7633
2025-08-30 20:45:34,972 - INFO - 文本清理：原始长度 5436，清理后长度 5396
2025-08-30 20:45:35,009 - INFO - 文本清理：原始长度 111994，清理后长度 108328
2025-08-30 20:45:35,010 - INFO - 文本清理：原始长度 3289，清理后长度 1500
2025-08-30 20:45:35,022 - INFO - 文本清理：原始长度 39723，清理后长度 39360
2025-08-30 20:45:35,025 - INFO - 文本清理：原始长度 12354，清理后长度 12198
2025-08-30 20:45:35,030 - INFO - 文本清理：原始长度 20348，清理后长度 20290
2025-08-30 20:45:35,200 - INFO - 文本清理：原始长度 553710，清理后长度 539858
2025-08-30 20:45:35,205 - INFO - 文本清理：原始长度 12661，清理后长度 12558
2025-08-30 20:45:35,207 - INFO - 文本清理：原始长度 8169，清理后长度 8138
2025-08-30 20:45:35,211 - INFO - 文本清理：原始长度 14121，清理后长度 14042
2025-08-30 20:45:35,213 - INFO - 文本清理：原始长度 7980，清理后长度 7925
2025-08-30 20:45:35,215 - INFO - 文本清理：原始长度 8816，清理后长度 8702
2025-08-30 20:45:35,216 - INFO - 文本清理：原始长度 4526，清理后长度 4495
2025-08-30 20:45:35,226 - INFO - 文本清理：原始长度 31882，清理后长度 30809
2025-08-30 20:45:35,229 - INFO - 文本清理：原始长度 9836，清理后长度 9769
2025-08-30 20:45:35,229 - INFO - 文本清理：原始长度 542，清理后长度 327
2025-08-30 20:45:35,276 - INFO - 文本清理：原始长度 197859，清理后长度 192065
2025-08-30 20:45:35,286 - INFO - 文本清理：原始长度 45602，清理后长度 43361
2025-08-30 20:45:35,296 - INFO - 文本清理：原始长度 45278，清理后长度 44007
2025-08-30 20:45:35,298 - INFO - 文本清理：原始长度 3919，清理后长度 3858
2025-08-30 20:45:35,300 - INFO - 文本清理：原始长度 4048，清理后长度 4011
2025-08-30 20:45:35,302 - INFO - 文本清理：原始长度 8664，清理后长度 8624
2025-08-30 20:45:35,318 - INFO - 文本清理：原始长度 62289，清理后长度 62159
2025-08-30 20:45:35,338 - INFO - 文本清理：原始长度 97983，清理后长度 96624
2025-08-30 20:45:35,342 - INFO - 文本清理：原始长度 26485，清理后长度 26333
2025-08-30 20:45:35,344 - INFO - 文本清理：原始长度 3249，清理后长度 2066
2025-08-30 20:45:35,344 - INFO - 文本清理：原始长度 886，清理后长度 595
2025-08-30 20:45:35,353 - INFO - 文本清理：原始长度 33721，清理后长度 32869
2025-08-30 20:45:35,356 - INFO - 文本清理：原始长度 11144，清理后长度 5009
2025-08-30 20:45:35,364 - INFO - 文本清理：原始长度 34473，清理后长度 33575
2025-08-30 20:45:35,395 - INFO - 文本清理：原始长度 99705，清理后长度 96455
2025-08-30 20:45:35,398 - INFO - 文本清理：原始长度 7422，清理后长度 7361
2025-08-30 20:45:35,428 - INFO - 文本清理：原始长度 119663，清理后长度 117139
2025-08-30 20:45:35,432 - INFO - 文本清理：原始长度 19550，清理后长度 19432
2025-08-30 20:45:35,441 - INFO - 文本清理：原始长度 42196，清理后长度 41001
2025-08-30 20:45:35,449 - INFO - 文本清理：原始长度 33535，清理后长度 32680
2025-08-30 20:45:35,459 - INFO - 文本清理：原始长度 41725，清理后长度 40794
2025-08-30 20:45:35,462 - INFO - 文本清理：原始长度 12331，清理后长度 12244
2025-08-30 20:45:35,462 - INFO - 第0-199条记录解析完成，获得199条记录
2025-08-30 20:45:35,463 - INFO - 正在获取文档总数...
2025-08-30 20:45:35,463 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-30 20:45:35,463 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-30 20:45:35,611 - INFO - GetDocNum响应状态码: 200
2025-08-30 20:45:35,611 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-30 20:45:35,611 - INFO - 响应内容长度: 5
2025-08-30 20:45:35,612 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-30 20:45:35,612 - INFO - 响应内容前200字符: 39560
2025-08-30 20:45:35,612 - INFO - 使用默认文档总数: 3000
2025-08-30 20:45:35,632 - INFO - 127.0.0.1 - - [30/Aug/2025 20:45:35] "GET /api/filter_options HTTP/1.1" 200 -
2025-08-30 20:45:44,188 - INFO - 收到数据请求: {'page': 1, 'limit': 20, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-30 20:45:44,188 - INFO - 增强搜索请求，参数: page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-30 20:45:44,189 - INFO - 实时API请求，参数: page=1, limit=20, keyword=''
2025-08-30 20:45:44,189 - INFO - 正在获取第0-19条记录...
2025-08-30 20:45:44,189 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-30 20:45:44,189 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '19'}
2025-08-30 20:45:44,484 - INFO - 响应状态码: 200
2025-08-30 20:45:44,484 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-30 20:45:44,485 - INFO - 响应内容长度: 437663
2025-08-30 20:45:44,485 - INFO - 检测到JSON格式响应，尝试解析
2025-08-30 20:45:44,488 - INFO - 成功解析JSON响应
2025-08-30 20:45:44,488 - INFO - 响应是数组格式，包含19条记录
2025-08-30 20:45:44,524 - INFO - 文本清理：原始长度 142449，清理后长度 139053
2025-08-30 20:45:44,527 - INFO - 文本清理：原始长度 7368，清理后长度 7310
2025-08-30 20:45:44,529 - INFO - 文本清理：原始长度 4525，清理后长度 4462
2025-08-30 20:45:44,530 - INFO - 文本清理：原始长度 1500，清理后长度 746
2025-08-30 20:45:44,531 - INFO - 文本清理：原始长度 2353，清理后长度 1187
2025-08-30 20:45:44,533 - INFO - 文本清理：原始长度 8907，清理后长度 8867
2025-08-30 20:45:44,535 - INFO - 文本清理：原始长度 12838，清理后长度 12760
2025-08-30 20:45:44,542 - INFO - 文本清理：原始长度 39614，清理后长度 39185
2025-08-30 20:45:44,544 - INFO - 文本清理：原始长度 6166，清理后长度 6085
2025-08-30 20:45:44,546 - INFO - 文本清理：原始长度 5005，清理后长度 4946
2025-08-30 20:45:44,547 - INFO - 文本清理：原始长度 4857，清理后长度 1725
2025-08-30 20:45:44,547 - INFO - 文本清理：原始长度 1537，清理后长度 806
2025-08-30 20:45:44,557 - INFO - 文本清理：原始长度 43547，清理后长度 42707
2025-08-30 20:45:44,558 - INFO - 文本清理：原始长度 5120，清理后长度 5047
2025-08-30 20:45:44,561 - INFO - 文本清理：原始长度 14023，清理后长度 4435
2025-08-30 20:45:44,562 - INFO - 文本清理：原始长度 2736，清理后长度 1183
2025-08-30 20:45:44,572 - INFO - 文本清理：原始长度 39116，清理后长度 38200
2025-08-30 20:45:44,580 - INFO - 文本清理：原始长度 30672，清理后长度 30507
2025-08-30 20:45:44,588 - INFO - 文本清理：原始长度 36472，清理后长度 36273
2025-08-30 20:45:44,588 - INFO - 第0-19条记录解析完成，获得19条记录
2025-08-30 20:45:44,589 - INFO - 正在获取文档总数...
2025-08-30 20:45:44,589 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-30 20:45:44,589 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-30 20:45:44,732 - INFO - GetDocNum响应状态码: 200
2025-08-30 20:45:44,732 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-30 20:45:44,732 - INFO - 响应内容长度: 5
2025-08-30 20:45:44,733 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-30 20:45:44,733 - INFO - 响应内容前200字符: 39560
2025-08-30 20:45:44,733 - INFO - 使用默认文档总数: 3000
2025-08-30 20:45:44,769 - INFO - 文本清理：原始长度 142449，清理后长度 139053
2025-08-30 20:45:44,772 - INFO - 文本清理：原始长度 7368，清理后长度 7310
2025-08-30 20:45:44,773 - INFO - 文本清理：原始长度 4525，清理后长度 4462
2025-08-30 20:45:44,774 - INFO - 文本清理：原始长度 1500，清理后长度 746
2025-08-30 20:45:44,774 - INFO - 文本清理：原始长度 2353，清理后长度 1187
2025-08-30 20:45:44,777 - INFO - 文本清理：原始长度 8907，清理后长度 8867
2025-08-30 20:45:44,779 - INFO - 文本清理：原始长度 12838，清理后长度 12760
2025-08-30 20:45:44,787 - INFO - 文本清理：原始长度 39614，清理后长度 39185
2025-08-30 20:45:44,789 - INFO - 文本清理：原始长度 6166，清理后长度 6085
2025-08-30 20:45:44,791 - INFO - 文本清理：原始长度 5005，清理后长度 4946
2025-08-30 20:45:44,793 - INFO - 文本清理：原始长度 4857，清理后长度 1725
2025-08-30 20:45:44,793 - INFO - 文本清理：原始长度 1537，清理后长度 806
2025-08-30 20:45:44,803 - INFO - 文本清理：原始长度 43547，清理后长度 42707
2025-08-30 20:45:44,804 - INFO - 文本清理：原始长度 5120，清理后长度 5047
2025-08-30 20:45:44,807 - INFO - 文本清理：原始长度 14023，清理后长度 4435
2025-08-30 20:45:44,808 - INFO - 文本清理：原始长度 2736，清理后长度 1183
2025-08-30 20:45:44,818 - INFO - 文本清理：原始长度 39116，清理后长度 38200
2025-08-30 20:45:44,825 - INFO - 文本清理：原始长度 30672，清理后长度 30507
2025-08-30 20:45:44,834 - INFO - 文本清理：原始长度 36472，清理后长度 36273
2025-08-30 20:45:44,834 - INFO - 搜索结果: 原始3000条，筛选后19条
2025-08-30 20:45:44,835 - INFO - 成功返回 19 条OA记录，总计 3000 条
2025-08-30 20:45:44,841 - INFO - 127.0.0.1 - - [30/Aug/2025 20:45:44] "GET /api/get_oa_data?page=1&limit=20&keyword= HTTP/1.1" 200 -
2025-08-30 20:45:56,504 - INFO - 使用本地缓存目录: cache
2025-08-30 20:45:56,504 - INFO - 收藏夹管理器初始化成功
2025-08-30 20:45:56,504 - INFO - 127.0.0.1 - - [30/Aug/2025 20:45:56] "GET /api/favorites HTTP/1.1" 200 -
2025-08-30 22:46:29,625 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\text_cleaner.py', reloading
2025-08-30 22:46:29,634 - INFO -  * Detected change in 'D:\\STU\\OA-2\\deploy\\text_cleaner.py', reloading
2025-08-30 22:46:30,235 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-30 22:46:30,710 - INFO - OA爬虫实例初始化成功
2025-08-30 22:46:30,710 - INFO - 启动OA信息Web应用 (开发环境)...
2025-08-30 22:46:30,710 - INFO - 请访问: http://localhost:5035
2025-08-30 22:46:30,726 - WARNING -  * Debugger is active!
2025-08-30 22:46:30,732 - INFO -  * Debugger PIN: 145-413-362
2025-08-30 22:48:05,730 - INFO - 收到数据请求: {'page': 1, 'limit': 1, 'keyword': '', 'unit': '', 'dept': '', 'start_date': '', 'end_date': '', 'author': '', 'min_length': '', 'max_length': ''}
2025-08-30 22:48:05,730 - INFO - 使用本地缓存目录: cache/app_cache
2025-08-30 22:48:05,731 - INFO - 缓存清理线程已启动
2025-08-30 22:48:05,732 - INFO - 缓存管理器初始化完成
2025-08-30 22:48:05,732 - INFO - 缓存管理器初始化成功
2025-08-30 22:48:05,732 - INFO - 增强搜索请求，参数: page=1, limit=1, keyword='', unit='', dept='', start_date='', end_date='', author=''
2025-08-30 22:48:05,732 - INFO - 实时API请求，参数: page=1, limit=1, keyword=''
2025-08-30 22:48:05,732 - INFO - 正在获取第0-0条记录...
2025-08-30 22:48:05,732 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
2025-08-30 22:48:05,733 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': '', 'row_start': '0', 'row_end': '0'}
2025-08-30 22:48:05,911 - INFO - 响应状态码: 200
2025-08-30 22:48:05,911 - INFO - 响应Content-Type: unknown
2025-08-30 22:48:05,911 - INFO - 响应内容长度: 0
2025-08-30 22:48:05,911 - WARNING - 服务器返回空响应
2025-08-30 22:48:05,911 - WARNING - 第0-0条记录返回错误: empty_response - 服务器返回空响应
2025-08-30 22:48:05,911 - INFO - 正在获取文档总数...
2025-08-30 22:48:05,911 - INFO - 请求URL: http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum
2025-08-30 22:48:05,913 - INFO - 请求数据: {'token': 'STU_OA_TOKEN_2024', 'subcompany_id': '0', 'keyword': ''}
2025-08-30 22:48:06,073 - INFO - GetDocNum响应状态码: 200
2025-08-30 22:48:06,073 - INFO - 响应Content-Type: text/html; charset=utf-8
2025-08-30 22:48:06,074 - INFO - 响应内容长度: 5
2025-08-30 22:48:06,074 - WARNING - 服务器返回HTML错误页面，可能是数据库连接问题
2025-08-30 22:48:06,074 - INFO - 响应内容前200字符: 39560
2025-08-30 22:48:06,074 - INFO - 使用默认文档总数: 3000
2025-08-30 22:48:06,074 - INFO - 搜索结果: 原始3000条，筛选后1条
2025-08-30 22:48:06,075 - INFO - 成功返回 1 条OA记录，总计 3000 条
2025-08-30 22:48:06,075 - INFO - 127.0.0.1 - - [30/Aug/2025 22:48:06] "GET /api/get_oa_data?page=1&limit=1&keyword= HTTP/1.1" 200 -
