#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收藏夹管理模块
用于管理用户收藏的OA信息
"""

import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional

class FavoritesManager:
    def __init__(self, favorites_file: str = None):
        # 使用持久化目录存储收藏夹数据
        if favorites_file is None:
            # 确定缓存目录 - 优先使用持久化目录，回退到本地目录
            render_cache_dir = "/opt/render/project/cache"
            if os.path.exists("/opt/render/project") or os.environ.get('FLASK_ENV') == 'production':
                # 云端环境或生产环境
                cache_dir = render_cache_dir
                os.makedirs(cache_dir, exist_ok=True)
                logging.info(f"使用云端持久化目录: {cache_dir}")
            else:
                # 本地开发环境
                cache_dir = "cache"
                os.makedirs(cache_dir, exist_ok=True)
                logging.info(f"使用本地缓存目录: {cache_dir}")
            
            favorites_file = os.path.join(cache_dir, "favorites.json")
        
        self.favorites_file = favorites_file
        self.favorites = self.load_favorites()
        
    def load_favorites(self) -> List[Dict]:
        """加载收藏夹数据"""
        try:
            if os.path.exists(self.favorites_file):
                with open(self.favorites_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logging.error(f"加载收藏夹失败: {e}")
            return []
    
    def save_favorites(self) -> bool:
        """保存收藏夹数据"""
        try:
            with open(self.favorites_file, 'w', encoding='utf-8') as f:
                json.dump(self.favorites, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logging.error(f"保存收藏夹失败: {e}")
            return False
    
    def add_favorite(self, item: Dict) -> bool:
        """添加收藏"""
        try:
            # 检查是否已经收藏
            doc_id = item.get('doc_id') or item.get('raw_data', {}).get('DOCID')
            if self.is_favorite(doc_id):
                return False
            
            # 创建收藏项
            favorite_item = {
                'id': doc_id,
                'title': item.get('doc_title', '') or item.get('raw_data', {}).get('DOCTITLE', ''),
                'content': item.get('doc_content', '') or item.get('raw_data', {}).get('DOCCONTENT', ''),
                'unit': item.get('doc_unit', '') or item.get('raw_data', {}).get('DOCUNIT', ''),
                'dept': item.get('raw_data', {}).get('DEPARTMENTNAME', ''),
                'author': item.get('raw_data', {}).get('LASTNAME', ''),
                'date': item.get('doc_date', '') or item.get('raw_data', {}).get('DOCDATE', ''),
                'raw_data': item.get('raw_data', {}),
                'added_time': datetime.now().isoformat(),
                'tags': []
            }
            
            self.favorites.append(favorite_item)
            return self.save_favorites()
        except Exception as e:
            logging.error(f"添加收藏失败: {e}")
            return False
    
    def remove_favorite(self, doc_id: str) -> bool:
        """移除收藏"""
        try:
            original_length = len(self.favorites)
            self.favorites = [fav for fav in self.favorites if fav.get('id') != doc_id]
            
            if len(self.favorites) < original_length:
                return self.save_favorites()
            return False
        except Exception as e:
            logging.error(f"移除收藏失败: {e}")
            return False
    
    def is_favorite(self, doc_id: str) -> bool:
        """检查是否已收藏"""
        return any(fav.get('id') == doc_id for fav in self.favorites)
    
    def get_favorites(self, limit: int = 50, offset: int = 0) -> Dict:
        """获取收藏列表"""
        try:
            total = len(self.favorites)
            # 按添加时间倒序排列
            sorted_favorites = sorted(self.favorites, key=lambda x: x.get('added_time', ''), reverse=True)
            
            # 分页
            favorites_slice = sorted_favorites[offset:offset + limit]
            
            return {
                'favorites': favorites_slice,
                'total': total,
                'offset': offset,
                'limit': limit,
                'has_more': offset + limit < total
            }
        except Exception as e:
            logging.error(f"获取收藏列表失败: {e}")
            return {'favorites': [], 'total': 0, 'offset': 0, 'limit': limit, 'has_more': False}
    
    def search_favorites(self, keyword: str, limit: int = 50, offset: int = 0) -> Dict:
        """搜索收藏"""
        try:
            keyword = keyword.lower()
            
            # 筛选包含关键词的收藏
            filtered_favorites = []
            for fav in self.favorites:
                if (keyword in fav.get('title', '').lower() or 
                    keyword in fav.get('content', '').lower() or
                    keyword in fav.get('unit', '').lower() or
                    keyword in fav.get('dept', '').lower() or
                    keyword in fav.get('author', '').lower()):
                    filtered_favorites.append(fav)
            
            total = len(filtered_favorites)
            # 按添加时间倒序排列
            sorted_favorites = sorted(filtered_favorites, key=lambda x: x.get('added_time', ''), reverse=True)
            
            # 分页
            favorites_slice = sorted_favorites[offset:offset + limit]
            
            return {
                'favorites': favorites_slice,
                'total': total,
                'offset': offset,
                'limit': limit,
                'has_more': offset + limit < total
            }
        except Exception as e:
            logging.error(f"搜索收藏失败: {e}")
            return {'favorites': [], 'total': 0, 'offset': 0, 'limit': limit, 'has_more': False}
    
    def add_tag(self, doc_id: str, tag: str) -> bool:
        """为收藏项添加标签"""
        try:
            for fav in self.favorites:
                if fav.get('id') == doc_id:
                    if 'tags' not in fav:
                        fav['tags'] = []
                    if tag not in fav['tags']:
                        fav['tags'].append(tag)
                    return self.save_favorites()
            return False
        except Exception as e:
            logging.error(f"添加标签失败: {e}")
            return False
    
    def remove_tag(self, doc_id: str, tag: str) -> bool:
        """移除收藏项的标签"""
        try:
            for fav in self.favorites:
                if fav.get('id') == doc_id and 'tags' in fav:
                    if tag in fav['tags']:
                        fav['tags'].remove(tag)
                        return self.save_favorites()
            return False
        except Exception as e:
            logging.error(f"移除标签失败: {e}")
            return False
    
    def get_tags(self) -> List[str]:
        """获取所有标签"""
        try:
            tags = set()
            for fav in self.favorites:
                if 'tags' in fav:
                    tags.update(fav['tags'])
            return sorted(list(tags))
        except Exception as e:
            logging.error(f"获取标签失败: {e}")
            return []
    
    def get_favorites_by_tag(self, tag: str, limit: int = 50, offset: int = 0) -> Dict:
        """根据标签获取收藏"""
        try:
            filtered_favorites = [fav for fav in self.favorites if tag in fav.get('tags', [])]
            
            total = len(filtered_favorites)
            # 按添加时间倒序排列
            sorted_favorites = sorted(filtered_favorites, key=lambda x: x.get('added_time', ''), reverse=True)
            
            # 分页
            favorites_slice = sorted_favorites[offset:offset + limit]
            
            return {
                'favorites': favorites_slice,
                'total': total,
                'offset': offset,
                'limit': limit,
                'has_more': offset + limit < total
            }
        except Exception as e:
            logging.error(f"根据标签获取收藏失败: {e}")
            return {'favorites': [], 'total': 0, 'offset': 0, 'limit': limit, 'has_more': False}