class FavoritesManager {
    constructor() {
        this.currentPage = 1;
        this.currentLimit = 20;
        this.currentKeyword = '';
        this.currentTag = '';
        this.isLoading = false;
        this.currentDocIdForTag = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFavorites();
        this.loadTags();
    }

    bindEvents() {
        // 收藏夹标签切换
        const favoritesTab = document.querySelector('[data-mode="favorites"]');
        if (favoritesTab) {
            favoritesTab.addEventListener('click', () => {
                this.showFavoritesPanel();
            });
        }

        // 搜索收藏
        const searchBtn = document.getElementById('favoritesSearchBtn');
        const searchInput = document.getElementById('favoritesSearch');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.searchFavorites();
            });
        }

        if (searchInput) {
            searchInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.searchFavorites();
                }
            });
        }

        // 标签输入模态框
        const tagInputModal = document.getElementById('tagInputModal');
        const tagInputClose = document.getElementById('tagInputClose');
        const tagInputCancel = document.getElementById('tagInputCancel');
        const tagInputSave = document.getElementById('tagInputSave');
        const tagInputField = document.getElementById('tagInputField');

        if (tagInputClose) {
            tagInputClose.addEventListener('click', () => {
                this.hideTagInputModal();
            });
        }

        if (tagInputCancel) {
            tagInputCancel.addEventListener('click', () => {
                this.hideTagInputModal();
            });
        }

        if (tagInputSave) {
            tagInputSave.addEventListener('click', () => {
                this.saveTag();
            });
        }

        if (tagInputField) {
            tagInputField.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.saveTag();
                }
            });
        }

        // 点击模态框外部关闭
        if (tagInputModal) {
            tagInputModal.addEventListener('click', (e) => {
                if (e.target === tagInputModal) {
                    this.hideTagInputModal();
                }
            });
        }
    }

    async loadFavorites() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        try {
            const params = new URLSearchParams();
            params.append('page', this.currentPage);
            params.append('limit', this.currentLimit);
            
            if (this.currentKeyword) {
                params.append('keyword', this.currentKeyword);
            }
            
            if (this.currentTag) {
                params.append('tag', this.currentTag);
            }

            const response = await fetch(`/api/favorites?${params.toString()}`);
            if (response.ok) {
                const result = await response.json();
                this.renderFavorites(result);
                this.updatePagination(result);
                this.updateStats(result);
            }
        } catch (error) {
            console.error('加载收藏失败:', error);
        } finally {
            this.isLoading = false;
        }
    }

    async loadTags() {
        try {
            const response = await fetch('/api/favorites/tags');
            if (response.ok) {
                const result = await response.json();
                this.renderTags(result.tags);
            }
        } catch (error) {
            console.error('加载标签失败:', error);
        }
    }

    renderFavorites(result) {
        const container = document.getElementById('favoritesContent');
        if (!container) return;

        if (!result.favorites || result.favorites.length === 0) {
            container.innerHTML = `
                <div class="empty-favorites">
                    <i class="fas fa-star"></i>
                    <h3>暂无收藏</h3>
                    <p>您可以收藏感兴趣的信息，方便以后查看</p>
                </div>
            `;
            return;
        }

        container.innerHTML = result.favorites.map(fav => `
            <div class="favorite-item" data-doc-id="${fav.id}">
                <div class="favorite-header">
                    <h4 class="favorite-title">${fav.title}</h4>
                    <div class="favorite-actions">
                        <button class="favorite-btn favorite-view" onclick="viewFavoriteContent('${fav.id}')">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button class="favorite-btn favorite-remove" onclick="removeFavorite('${fav.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
                <div class="favorite-meta">
                    <span>发布单位: ${fav.unit || '未知'}</span>
                    <span>发布部门: ${fav.dept || '未知'}</span>
                    <span>发布人: ${fav.author || '未知'}</span>
                    <span>发布日期: ${fav.date || '未知'}</span>
                </div>
                <div class="favorite-content">
                    ${fav.content || '无内容'}
                </div>
                <div class="favorite-item-tags">
                    ${(fav.tags || []).map(tag => `
                        <span class="favorite-item-tag">
                            ${tag}
                            <i class="fas fa-times" onclick="removeTag('${fav.id}', '${tag}')" style="margin-left: 5px; cursor: pointer;"></i>
                        </span>
                    `).join('')}
                    <button class="add-tag-btn" onclick="showTagInputModal('${fav.id}')">
                        <i class="fas fa-plus"></i> 添加标签
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderTags(tags) {
        const container = document.getElementById('favoritesTags');
        if (!container) return;

        if (!tags || tags.length === 0) {
            container.innerHTML = '<p style="color: var(--subtle-text-color); font-size: 14px;">暂无标签</p>';
            return;
        }

        container.innerHTML = tags.map(tag => `
            <span class="favorite-tag ${this.currentTag === tag ? 'active' : ''}" onclick="filterByTag('${tag}')">
                ${tag}
            </span>
        `).join('');
    }

    updatePagination(result) {
        const pagination = document.getElementById('favoritesPagination');
        if (!pagination) return;

        if (result.total <= this.currentLimit) {
            pagination.style.display = 'none';
            return;
        }

        const hasPrev = this.currentPage > 1;
        const hasNext = result.has_more;

        pagination.innerHTML = `
            <button onclick="window.favoritesManager.goToPage(${this.currentPage - 1})" ${!hasPrev ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
            <span class="page-info">第 ${this.currentPage} 页</span>
            <button onclick="window.favoritesManager.goToPage(${this.currentPage + 1})" ${!hasNext ? 'disabled' : ''}>
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        `;

        pagination.style.display = 'flex';
    }

    updateStats(result) {
        const countElement = document.getElementById('favoritesCount');
        if (countElement) {
            countElement.textContent = result.total || 0;
        }
    }

    showFavoritesPanel() {
        // 切换到收藏夹标签
        document.querySelectorAll('.search-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector('[data-mode="favorites"]').classList.add('active');

        // 显示收藏夹面板
        document.getElementById('favoritesPanel').style.display = 'block';
        document.getElementById('searchHistoryPanel').style.display = 'none';

        // 重新加载收藏数据
        this.loadFavorites();
    }

    searchFavorites() {
        const searchInput = document.getElementById('favoritesSearch');
        if (searchInput) {
            this.currentKeyword = searchInput.value.trim();
            this.currentTag = ''; // 清除标签筛选
            this.currentPage = 1;
            this.loadFavorites();
        }
    }

    goToPage(page) {
        if (page < 1) return;
        this.currentPage = page;
        this.loadFavorites();
    }

    showTagInputModal(docId) {
        this.currentDocIdForTag = docId;
        const modal = document.getElementById('tagInputModal');
        const input = document.getElementById('tagInputField');
        
        if (modal && input) {
            modal.classList.add('active');
            input.value = '';
            input.focus();
        }
    }

    hideTagInputModal() {
        const modal = document.getElementById('tagInputModal');
        if (modal) {
            modal.classList.remove('active');
        }
        this.currentDocIdForTag = null;
    }

    async saveTag() {
        if (!this.currentDocIdForTag) return;

        const input = document.getElementById('tagInputField');
        const tag = input.value.trim();
        
        if (!tag) {
            alert('请输入标签名称');
            return;
        }

        try {
            const response = await fetch(`/api/favorites/${this.currentDocIdForTag}/tags`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ tag })
            });

            if (response.ok) {
                this.hideTagInputModal();
                this.loadFavorites(); // 重新加载收藏列表
                this.loadTags(); // 重新加载标签列表
            } else {
                alert('添加标签失败');
            }
        } catch (error) {
            console.error('添加标签失败:', error);
            alert('添加标签失败');
        }
    }

    async removeTag(docId, tag) {
        if (!confirm(`确定要删除标签"${tag}"吗？`)) return;

        try {
            const response = await fetch(`/api/favorites/${docId}/tags/${tag}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.loadFavorites(); // 重新加载收藏列表
                this.loadTags(); // 重新加载标签列表
            } else {
                alert('删除标签失败');
            }
        } catch (error) {
            console.error('删除标签失败:', error);
            alert('删除标签失败');
        }
    }
}

// 全局函数
function filterByTag(tag) {
    if (window.favoritesManager) {
        window.favoritesManager.currentTag = tag;
        window.favoritesManager.currentKeyword = '';
        window.favoritesManager.currentPage = 1;
        window.favoritesManager.loadFavorites();
    }
}

function viewFavoriteContent(docId) {
    // 获取收藏详情并显示
    fetch(`/api/favorites/${docId}`)
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('获取收藏详情失败');
            }
        })
        .then(favorite => {
            // 使用全局的showFullContent函数显示内容
            if (window.showFullContent) {
                window.showFullContent(
                    favorite.title,
                    favorite.content,
                    favorite.unit,
                    favorite.dept,
                    favorite.author,
                    favorite.date
                );
            } else {
                // 如果全局函数不存在，使用模态框显示
                const modal = document.getElementById('contentModal');
                const modalTitle = document.getElementById('modalTitle');
                const modalMeta = document.getElementById('modalMeta');
                const modalBody = document.getElementById('modalBody');
                
                if (modal && modalTitle && modalMeta && modalBody) {
                    modalTitle.textContent = favorite.title || '无标题';
                    modalMeta.innerHTML = `
                        <div class="modal-meta-item">发布单位: ${favorite.unit || '未知'}</div>
                        <div class="modal-meta-item">发布部门: ${favorite.dept || '未知'}</div>
                        <div class="modal-meta-item">发布人: ${favorite.author || '未知'}</div>
                        <div class="modal-meta-item">发布日期: ${favorite.date || '未知'}</div>
                    `;
                    modalBody.innerHTML = `<div class="standardized-content">${favorite.content || '无内容'}</div>`;
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            }
        })
        .catch(error => {
            console.error('查看收藏内容失败:', error);
            alert('查看收藏内容失败');
        });
}

async function removeFavorite(docId) {
    if (!confirm('确定要删除这条收藏吗？')) return;

    try {
        const response = await fetch(`/api/favorites/${docId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            if (window.favoritesManager) {
                window.favoritesManager.loadFavorites();
            }
        } else {
            alert('删除收藏失败');
        }
    } catch (error) {
        console.error('删除收藏失败:', error);
        alert('删除收藏失败');
    }
}

function showTagInputModal(docId) {
    if (window.favoritesManager) {
        window.favoritesManager.showTagInputModal(docId);
    }
}

// 收藏功能相关函数
async function toggleFavorite(button, event) {
    event.stopPropagation();
    
    const itemElement = button.closest('.data-item');
    if (!itemElement) return;

    const docId = itemElement.dataset.docId;
    const title = itemElement.dataset.title;
    const content = itemElement.dataset.content;
    const unit = itemElement.dataset.unit;
    const dept = itemElement.dataset.dept;
    const author = itemElement.dataset.author;
    const date = itemElement.dataset.date;

    if (!docId) {
        alert('无法收藏：缺少文档ID');
        return;
    }

    try {
        if (button.classList.contains('add')) {
            // 添加收藏
            const favoriteData = {
                doc_id: docId,
                doc_title: title,
                doc_content: content,
                doc_unit: unit,
                raw_data: {
                    DOCID: docId,
                    DOCTITLE: title,
                    DOCCONTENT: content,
                    DOCUNIT: unit,
                    DEPARTMENTNAME: dept,
                    LASTNAME: author,
                    DOCDATE: date
                }
            };

            const response = await fetch('/api/favorites', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(favoriteData)
            });

            if (response.ok) {
                button.classList.remove('add');
                button.classList.add('remove');
                button.innerHTML = '<i class="fas fa-star"></i> 取消收藏';
                
                // 显示成功提示
                showToast('收藏成功');
            } else {
                alert('收藏失败');
            }
        } else {
            // 取消收藏
            const response = await fetch(`/api/favorites/${docId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                button.classList.remove('remove');
                button.classList.add('add');
                button.innerHTML = '<i class="fas fa-star"></i> 收藏';
                
                // 显示成功提示
                showToast('取消收藏成功');
            } else {
                alert('取消收藏失败');
            }
        }
    } catch (error) {
        console.error('收藏操作失败:', error);
        alert('操作失败');
    }
}

async function checkFavoriteStatus(docId, button) {
    if (!docId || !button) return;

    try {
        const response = await fetch(`/api/favorites/${docId}/check`);
        if (response.ok) {
            const result = await response.json();
            if (result.is_favorite) {
                button.classList.remove('add');
                button.classList.add('remove');
                button.innerHTML = '<i class="fas fa-star"></i> 取消收藏';
            }
        }
    } catch (error) {
        console.error('检查收藏状态失败:', error);
    }
}

function showToast(message) {
    // 创建一个简单的提示框
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4caf50;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        z-index: 10000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        font-size: 14px;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);

    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 初始化收藏夹管理器
document.addEventListener('DOMContentLoaded', () => {
    window.favoritesManager = new FavoritesManager();
});