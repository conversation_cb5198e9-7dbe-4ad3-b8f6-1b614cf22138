/**
 * 增强的文本清理工具 - 前端版本
 * 用于清理和过滤文本中的乱码字符
 */

class TextCleanerEnhanced {
    constructor() {
        // 初始化清理模式
        this.patterns = this.initPatterns();
        
        // 编码错误修复映射
        this.encodingFixes = {
            'â€œ': '"',     // 常见的编码错误
            'â€': '"',
            'â€™': "'",
            'â€˜': "'",
            'â€"': '—',
            'â€"': '–',
            'Â': '',        // 不需要的字符
            'â€¦': '…',
            'â„¢': '™',
            'Â©': '©',
            'Â®': '®',
            'Â°': '°',
            'Â±': '±',
            'Â²': '²',
            'Â³': '³',
            'ï¿½': '',      // Unicode replacement character in UTF-8
            '�': '',        // Unicode replacement character
            // 中文编码错误
            'æ': '',
            'ä¸': '',
            'äº': '',
            'å': '',
            'è': '',
            'ç': '',
            'é': '',
            'ê': '',
            'ë': '',
            'î': '',
            'ï': '',
            'ô': '',
            'ö': '',
            'û': '',
            'ü': ''
        };
        
        // HTML实体映射
        this.htmlEntities = {
            '&lt;': '<',
            '&gt;': '>',
            '&amp;': '&',
            '&quot;': '"',
            '&apos;': "'",
            '&nbsp;': ' ',
            '&ldquo;': '"',
            '&rdquo;': '"',
            '&lsquo;': "'",
            '&rsquo;': "'",
            '&mdash;': '—',
            '&ndash;': '–',
            '&hellip;': '…',
            'ldquo;': '"',
            'rdquo;': '"',
            'nbsp;': ' '
        };
    }
    
    initPatterns() {
        const patterns = {};
        
        // 控制字符（除了常用的换行、制表符等）
        patterns.controlChars = /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g;
        
        // 零宽字符
        patterns.zeroWidth = /[\u200B-\u200D\uFEFF]/g;
        
        // 私有使用区字符
        patterns.privateUse = /[\uE000-\uF8FF]/g;
        
        // 代理对字符（不完整的UTF-16）
        patterns.surrogates = /[\uD800-\uDFFF]/g;
        
        // 连续的非正常字符
        patterns.garbledSequence = /[^\w\s\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff\u31f0-\u31ff\u3200-\u32ff\u3300-\u33ff\uff00-\uffef\u2e80-\u2eff\u2f00-\u2fdf\u31c0-\u31ef\u2ff0-\u2fff\u3000-\u303f\u0020-\u007e\u00a0-\u00ff\u2010-\u2015\u2018-\u201f\u2020-\u2027\u2030-\u2038\u203b-\u203e\u2047-\u2049\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30fb\ufe30-\ufe44\ufe47-\ufe48\ufe50-\ufe57\ufe59-\ufe66\ufe68-\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a-\uff1b\uff1f-\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65！-～]{3,}/g;
        
        // HTML实体错误
        patterns.htmlEntities = /&[a-zA-Z0-9]+;/g;
        
        // 异常的Unicode序列
        patterns.invalidUnicode = /[\uFFFE\uFFFF]/g;
        
        // Office样式清理模式 - 更宽泛的匹配
        patterns.msoStyles = /mso-[^;>\s"']*[^;>\s"']*;?/gi;
        patterns.fontFamily = /font-family\s*:\s*[^;>\s"']*[^;>\s"']*;?/gi;
        patterns.chineseFonts = /(方正|微软|华文|宋体|黑体|楷体|仿宋|隶书|幼圆|新宋体|细明体)[^;>\s"']*/gi;
        patterns.cssStyles = /style\s*=\s*["'][^"']*["']/gi;
        patterns.cssClasses = /class\s*=\s*["'][^"']*["']/gi;
        patterns.styleAttributes = /(color|background|margin|padding|border|width|height|text-align|font-size|font-weight|text-decoration|line-height|letter-spacing|word-spacing)\s*:\s*[^;>\s"']*;?/gi;
        patterns.wordMarks = /<(\/?)(w:|o:|v:)[^>]*>/gi;
        patterns.emptyTags = /<(span|div|p|font)[^>]*>\s*<\/\1>/gi;
        
        return patterns;
    }
    
    /**
     * 清理文本中的乱码
     * @param {string} text - 要清理的文本
     * @param {boolean} aggressive - 是否使用激进模式
     * @returns {string} 清理后的文本
     */
    cleanText(text, aggressive = false) {
        if (!text) return '';
        
        // 确保是字符串
        text = String(text);
        const originalText = text;
        
        try {
            // 1. Unicode标准化（如果浏览器支持）
            if (text.normalize) {
                text = text.normalize('NFKC');
            }
            
            // 2. 修复常见编码错误
            text = this.fixEncodingErrors(text);
            
            // 3. 移除控制字符
            text = text.replace(this.patterns.controlChars, '');
            
            // 4. 移除零宽字符
            text = text.replace(this.patterns.zeroWidth, '');
            
            // 5. 移除私有使用区字符
            text = text.replace(this.patterns.privateUse, '');
            
            // 6. 移除代理对字符
            text = text.replace(this.patterns.surrogates, '');
            
            // 7. 移除无效Unicode字符
            text = text.replace(this.patterns.invalidUnicode, '');
            
            // 8. 清理HTML实体
            text = this.cleanHtmlEntities(text);
            
            // 9. 清理Office样式标记
            text = this.cleanOfficeStyles(text);
            
            // 10. 如果是激进模式，进行更多清理
            if (aggressive) {
                text = this.aggressiveClean(text);
            }
            
            // 10. 清理多余的空白字符
            text = text.replace(/\s+/g, ' ').trim();
            
            // 11. 检查是否清理过度
            if (text.length < originalText.length * 0.3 && originalText.length > 50) {
                // 如果清理后内容减少太多，使用温和模式重新清理
                text = this.gentleClean(originalText);
            }
            
            return text || '无内容';
            
        } catch (error) {
            console.error('文本清理过程中发生错误:', error);
            return this.simpleClean(originalText);
        }
    }
    
    /**
     * 修复常见的编码错误
     * @param {string} text - 要修复的文本
     * @returns {string} 修复后的文本
     */
    fixEncodingErrors(text) {
        for (const [error, fix] of Object.entries(this.encodingFixes)) {
            text = text.replace(new RegExp(this.escapeRegex(error), 'g'), fix);
        }
        return text;
    }
    
    /**
     * 清理HTML实体
     * @param {string} text - 要清理的文本
     * @returns {string} 清理后的文本
     */
    cleanHtmlEntities(text) {
        // 修复已知的HTML实体
        for (const [entity, replacement] of Object.entries(this.htmlEntities)) {
            text = text.replace(new RegExp(this.escapeRegex(entity), 'g'), replacement);
        }
        
        // 移除未知的HTML实体
        text = text.replace(this.patterns.htmlEntities, '');
        
        // 清理HTML标签
        text = text.replace(/<[^>]*>/g, '');
        
        return text;
    }
    
    /**
     * 清理Office样式标记
     * @param {string} text - 要清理的文本
     * @returns {string} 清理后的文本
     */
    cleanOfficeStyles(text) {
        try {
            // 1. 清理Microsoft Office特有样式
            text = text.replace(this.patterns.msoStyles, '');
            
            // 2. 清理字体家族声明
            text = text.replace(this.patterns.fontFamily, '');
            
            // 3. 清理中文字体名称
            text = text.replace(this.patterns.chineseFonts, '');
            
            // 4. 清理CSS样式属性
            text = text.replace(this.patterns.cssStyles, '');
            
            // 5. 清理CSS类名
            text = text.replace(this.patterns.cssClasses, '');
            
            // 6. 清理其他样式属性
            text = text.replace(this.patterns.styleAttributes, '');
            
            // 7. 清理Word特有标记
            text = text.replace(this.patterns.wordMarks, '');
            
            // 8. 清理空的样式标签
            text = text.replace(this.patterns.emptyTags, '');
            
            // 9. 清理常见的Office垃圾文本
            const officeJunkPatterns = [
                /mso-bidi-font-family[^;>\s"']*;?/gi,  // MSO双向字体
                /mso-[^;>\s"']*[^;>\s"']*;?/gi,       // 所有MSO样式
                /方正小标宋简体[^;>\s"']*/gi,              // 方正字体
                /微软雅黑[^;>\s"']*/gi,                   // 微软字体
                /宋体[^;>\s"']*/gi,                      // 宋体
                /font-family\s*:[^;>\s"']*[^;>\s"']*;?/gi,  // 字体家族
                /style\s*=\s*["'][^"']*mso[^"']*["']/gi,  // 包含mso的style
                /<\s*!\s*\[if[^\]]*\]>.*?<\s*!\s*\[endif\]\s*>/gi,  // IE条件注释
                /<\s*o:p\s*>.*?<\/\s*o:p\s*>/gi,     // Office段落标记
                /<\s*v:[^>]*>.*?<\/\s*v:[^>]*>/gi,   // VML标记
                /xmlns:[^=]*=[^>\s]*/gi,             // XML命名空间
                /class\s*=\s*["']Mso[^"']*["']/gi,  // Office CSS类
            ];
            
            officeJunkPatterns.forEach(pattern => {
                text = text.replace(pattern, '');
            });
            
            // 10. 清理多余的分号和冒号
            text = text.replace(/;+/g, ';');  // 多个分号
            text = text.replace(/:\s*;/g, '');  // 空的CSS属性
            text = text.replace(/;\s*}/g, '}');  // 样式块末尾多余分号
            
            // 11. 清理孤立的标点符号
            text = text.replace(/[;:]\s*(?=[<\s])/g, ' ');
            
            return text;
            
        } catch (error) {
            console.error('Office样式清理过程中发生错误:', error);
            return text;
        }
    }
    
    /**
     * 激进模式清理
     * @param {string} text - 要清理的文本
     * @returns {string} 清理后的文本
     */
    aggressiveClean(text) {
        // 移除连续的异常字符序列
        text = text.replace(this.patterns.garbledSequence, ' ');
        
        // 移除孤立的特殊字符
        text = text.replace(/(?<!\w)[^\w\s\u4e00-\u9fff](?!\w)/g, ' ');
        
        return text;
    }
    
    /**
     * 温和模式清理
     * @param {string} text - 要清理的文本
     * @returns {string} 清理后的文本
     */
    gentleClean(text) {
        // 只移除明显的乱码字符
        text = text.replace(this.patterns.controlChars, '');
        text = text.replace(this.patterns.zeroWidth, '');
        text = text.replace(this.patterns.surrogates, '');
        text = text.replace(/\s+/g, ' ');
        return text.trim();
    }
    
    /**
     * 简单清理（错误恢复用）
     * @param {string} text - 要清理的文本
     * @returns {string} 清理后的文本
     */
    simpleClean(text) {
        if (!text) return '无内容';
        
        // 只做最基本的清理
        text = text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, ''); // 移除控制字符
        text = text.replace(/\s+/g, ' '); // 规范化空白字符
        return text.trim() || '无内容';
    }
    
    /**
     * 检测乱码比例
     * @param {string} text - 要检测的文本
     * @returns {number} 乱码比例 (0.0 - 1.0)
     */
    detectGarbledRatio(text) {
        if (!text) return 0.0;
        
        const totalChars = text.length;
        let garbledChars = 0;
        
        // 统计各种异常字符
        garbledChars += (text.match(this.patterns.controlChars) || []).length;
        garbledChars += (text.match(this.patterns.zeroWidth) || []).length;
        garbledChars += (text.match(this.patterns.privateUse) || []).length;
        garbledChars += (text.match(this.patterns.surrogates) || []).length;
        garbledChars += (text.match(this.patterns.invalidUnicode) || []).length;
        
        // 检查编码错误模式
        for (const error of Object.keys(this.encodingFixes)) {
            const matches = text.split(error).length - 1;
            garbledChars += matches * error.length;
        }
        
        return Math.min(garbledChars / totalChars, 1.0);
    }
    
    /**
     * 判断文本是否可能包含乱码
     * @param {string} text - 要检查的文本
     * @param {number} threshold - 乱码比例阈值
     * @returns {boolean} 是否可能包含乱码
     */
    isLikelyGarbled(text, threshold = 0.1) {
        return this.detectGarbledRatio(text) > threshold;
    }
    
    /**
     * 转义正则表达式特殊字符
     * @param {string} string - 要转义的字符串
     * @returns {string} 转义后的字符串
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    /**
     * 清理标题
     * @param {string} title - 要清理的标题
     * @returns {string} 清理后的标题
     */
    cleanTitle(title) {
        if (!title) return '无标题';
        
        // 对标题使用温和清理
        let cleaned = this.cleanText(title, false);
        
        // 特殊处理标题中的常见问题
        cleaned = cleaned.replace(/^[^\u4e00-\u9fff\w]*/, ''); // 移除开头的非中文非字母数字字符
        cleaned = cleaned.replace(/[^\u4e00-\u9fff\w]*$/, ''); // 移除结尾的非中文非字母数字字符
        
        return cleaned || '无标题';
    }
    
    /**
     * 清理内容
     * @param {string} content - 要清理的内容
     * @param {boolean} preserveStructure - 是否保留结构
     * @returns {string} 清理后的内容
     */
    cleanContent(content, preserveStructure = true) {
        if (!content) return '无内容';
        
        let cleaned = this.cleanText(content, false);
        
        if (preserveStructure) {
            // 保留段落结构
            cleaned = cleaned.replace(/\n\s*\n/g, '\n\n');
        }
        
        return cleaned || '无内容';
    }
}

// 创建全局实例
const textCleanerEnhanced = new TextCleanerEnhanced();

// 导出便捷函数，覆盖原有的全局清理函数
window.cleanHtml = function(content, preserveStructure = false) {
    return textCleanerEnhanced.cleanContent(content, preserveStructure);
};

window.cleanTitle = function(title) {
    return textCleanerEnhanced.cleanTitle(title);
};

window.detectGarbledText = function(text, threshold = 0.1) {
    return textCleanerEnhanced.isLikelyGarbled(text, threshold);
};

window.cleanTextAdvanced = function(text, aggressive = false) {
    return textCleanerEnhanced.cleanText(text, aggressive);
};

// 添加调试函数
window.getGarbledRatio = function(text) {
    return textCleanerEnhanced.detectGarbledRatio(text);
};

console.log('增强的文本清理工具已加载');
