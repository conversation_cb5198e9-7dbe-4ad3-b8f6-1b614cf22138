#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA系统配置文件
"""

class Config:
    @staticmethod
    def get_oa_config():
        return {
            'api_url': 'http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc',
            'doc_num_url': 'http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum',
            'token': 'STU_OA_TOKEN_2024',  # 使用实际的token
            'timeout': 30,
            'records_per_batch': 100,
            'delay': 1,
            'base_url': 'http://wechat.stu.edu.cn/webservice_oa/oa_stu_'
        }