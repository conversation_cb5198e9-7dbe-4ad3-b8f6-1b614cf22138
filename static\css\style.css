/* OA数据抓取与可视化系统 - 样式文件 */
:root {
    --primary-color: #1976d2;
    --secondary-color: #f5f5f5;
    --text-color: #333;
    --subtle-text-color: #757575;
    --border-color: #e0e0e0;
    --shadow-color: rgba(0, 0, 0, 0.08);
    --accent-color: #2196f3;
    --highlight-color: #bbdefb;
    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #03a9f4;
}

/* 增强的搜索样式 */
.search-enhanced {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin: 0 auto 25px auto;
    box-shadow: 0 4px 12px var(--shadow-color);
    border: 1px solid var(--border-color);
    max-width: 1000px;
    width: 100%;
}

.search-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
}

.search-tab {
    padding: 12px 24px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: var(--subtle-text-color);
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.search-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.search-tab:hover {
    color: var(--primary-color);
    background: var(--secondary-color);
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    justify-content: center;
    position: relative;
}

.search-input-group {
    position: relative;
    flex: 1;
    min-width: 300px;
    max-width: 600px;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: white;
    box-sizing: border-box;
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px var(--highlight-color);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--subtle-text-color);
    font-size: 18px;
}

.search-button {
    padding: 15px 25px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.search-button:hover {
    background: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px var(--shadow-color);
}

.advanced-search-toggle {
    padding: 15px 20px;
    background: var(--secondary-color);
    color: var(--primary-color);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.advanced-search-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.advanced-search-panel {
    display: none;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.advanced-search-panel.active {
    display: grid;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.form-control {
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px var(--highlight-color);
}

.form-select {
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px var(--highlight-color);
}

/* 搜索建议 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px var(--shadow-color);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestions.active {
    display: block;
}

.suggestion-item {
    padding: 12px 20px;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.suggestion-item:hover {
    background: var(--secondary-color);
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* 搜索历史 */
.search-history-panel {
    display: none;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-top: 20px;
}

.search-history-panel.active {
    display: block;
}

.search-history-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.clear-history-btn {
    padding: 6px 12px;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-history-btn:hover {
    background: #d32f2f;
}

.history-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.history-item:hover {
    box-shadow: 0 2px 8px var(--shadow-color);
    transform: translateY(-1px);
}

.history-text {
    flex: 1;
    font-size: 14px;
    color: var(--text-color);
    word-break: break-word;
    margin-right: 10px;
}

.history-use-btn {
    padding: 6px 12px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.history-use-btn:hover {
    background: #1565c0;
}

.history-delete-btn {
    padding: 6px 12px;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-delete-btn:hover {
    background: #d32f2f;
}

.no-history {
    text-align: center;
    color: var(--subtle-text-color);
    font-style: italic;
    padding: 20px;
}

/* 搜索过滤器 */
.search-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-tag {
    padding: 8px 16px;
    background: var(--accent-color);
    color: white;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-tag .remove-filter {
    cursor: pointer;
    font-weight: bold;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.filter-tag .remove-filter:hover {
    opacity: 1;
}

/* 搜索统计 */
.search-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--secondary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.search-results-info {
    font-size: 14px;
    color: var(--text-color);
}

.search-sort {
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-sort select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }
    
    .search-input-group {
        min-width: 100%;
        max-width: 100%;
        margin-bottom: 10px;
    }
    
    .search-tabs {
        flex-wrap: wrap;
    }
    
    .search-tab {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .advanced-search-panel {
        grid-template-columns: 1fr;
    }
    
    .search-stats {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .search-button,
    .advanced-search-toggle {
        width: 100%;
        justify-content: center;
        margin-top: 5px;
    }
}

@media (max-width: 480px) {
    .search-enhanced {
        padding: 15px;
    }
    
    .search-tabs {
        flex-direction: column;
    }
    
    .search-tab {
        text-align: center;
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
    }
    
    .search-tab.active {
        border-bottom-color: var(--primary-color);
        border-left: 3px solid var(--primary-color);
    }
    
    .search-form {
        gap: 20px;
    }
    
    .search-input-group {
        margin-bottom: 15px;
    }
    
    .search-button {
        margin-top: 10px;
        padding: 12px 20px;
    }
    
    .advanced-search-toggle {
        margin-top: 5px;
    }
}

/* 加载动画 */
.search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--subtle-text-color);
}

.search-loading .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 高亮搜索结果 */
.highlight {
    background: var(--warning-color);
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

/* 搜索结果为空 */
.empty-search-results {
    text-align: center;
    padding: 40px;
    color: var(--subtle-text-color);
}

.empty-search-results i {
    font-size: 48px;
    margin-bottom: 20px;
    color: var(--border-color);
}

.empty-search-results h3 {
    margin-bottom: 10px;
    color: var(--text-color);
}

.empty-search-results p {
    margin-bottom: 20px;
}

.empty-search-results button {
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.empty-search-results button:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

/* 收藏夹功能样式 */
.favorites-panel {
    display: none;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-top: 20px;
}

.favorites-panel.active {
    display: block;
}

.favorites-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.favorites-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.favorites-search {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.favorites-search input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
}

.favorites-search button {
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.favorites-search button:hover {
    background: #1565c0;
}

.favorites-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.favorite-tag {
    padding: 4px 12px;
    background: var(--accent-color);
    color: white;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.favorite-tag:hover {
    background: var(--primary-color);
}

.favorite-tag.active {
    background: var(--primary-color);
}

.favorite-item {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.favorite-item:hover {
    box-shadow: 0 2px 8px var(--shadow-color);
    transform: translateY(-1px);
}

.favorite-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.favorite-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    flex: 1;
    margin-right: 10px;
}

.favorite-actions {
    display: flex;
    gap: 5px;
}

.favorite-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.favorite-remove {
    background: var(--error-color);
    color: white;
}

.favorite-remove:hover {
    background: #d32f2f;
}

.favorite-view {
    background: var(--info-color);
    color: white;
}

.favorite-view:hover {
    background: #0288d1;
}

.favorite-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    color: var(--subtle-text-color);
    font-size: 12px;
    margin-bottom: 8px;
}

.favorite-meta span {
    display: flex;
    align-items: center;
}

.favorite-meta span::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    background-color: var(--accent-color);
    border-radius: 50%;
    margin-right: 6px;
}

.favorite-content {
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.5;
    max-height: 60px;
    overflow: hidden;
    position: relative;
}

.favorite-content.expanded {
    max-height: none;
}

.favorite-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, white);
    pointer-events: none;
}

.favorite-content.expanded::after {
    display: none;
}

.favorite-item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 8px;
}

.favorite-item-tag {
    padding: 2px 8px;
    background: var(--secondary-color);
    color: var(--subtle-text-color);
    border-radius: 10px;
    font-size: 11px;
    border: 1px solid var(--border-color);
}

.add-tag-btn {
    padding: 2px 8px;
    background: transparent;
    color: var(--accent-color);
    border: 1px dashed var(--accent-color);
    border-radius: 10px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-tag-btn:hover {
    background: var(--accent-color);
    color: white;
}

/* 收藏按钮样式 */
.favorite-toggle {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.favorite-toggle.add {
    background: var(--warning-color);
    color: white;
}

.favorite-toggle.add:hover {
    background: #f57c00;
}

.favorite-toggle.remove {
    background: var(--error-color);
    color: white;
}

.favorite-toggle.remove:hover {
    background: #d32f2f;
}

.favorite-toggle i {
    font-size: 14px;
}

/* 收藏夹为空状态 */
.empty-favorites {
    text-align: center;
    padding: 40px;
    color: var(--subtle-text-color);
}

.empty-favorites i {
    font-size: 48px;
    margin-bottom: 20px;
    color: var(--border-color);
}

.empty-favorites h3 {
    margin-bottom: 10px;
    color: var(--text-color);
}

.empty-favorites p {
    margin-bottom: 20px;
}

/* 分页样式 */
.favorites-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.favorites-pagination button {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.favorites-pagination button:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
}

.favorites-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.favorites-pagination .page-info {
    color: var(--subtle-text-color);
    font-size: 14px;
}

/* 标签输入模态框 */
.tag-input-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.tag-input-modal.active {
    display: flex;
}

.tag-input-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
}

.tag-input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.tag-input-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.tag-input-close {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--secondary-color);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--subtle-text-color);
}

.tag-input-close:hover {
    background: var(--border-color);
}

.tag-input-field {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
}

.tag-input-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.tag-input-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.tag-input-save {
    background: var(--primary-color);
    color: white;
}

.tag-input-save:hover {
    background: #1565c0;
}

.tag-input-cancel {
    background: var(--secondary-color);
    color: var(--text-color);
}

.tag-input-cancel:hover {
    background: var(--border-color);
}

/* 项目操作按钮 */
.item-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

/* 缓存管理样式 */
.cache-panel {
    display: none;
    padding: 20px;
    background: var(--secondary-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-top: 20px;
}

.cache-panel.active {
    display: block;
}

.cache-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.cache-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.cache-actions {
    display: flex;
    gap: 10px;
}

.cache-stats {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.cache-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.cache-stat-item {
    text-align: center;
    padding: 10px;
    background: var(--secondary-color);
    border-radius: 6px;
}

.cache-stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
}

.cache-stat-label {
    font-size: 12px;
    color: var(--subtle-text-color);
    margin-top: 5px;
}

.cache-controls {
    margin-bottom: 20px;
}

.cache-controls h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-size: 16px;
}

.cache-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.cache-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.cache-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.cache-btn.clear-expired {
    background: var(--warning-color);
    color: white;
}

.cache-btn.clear-search {
    background: var(--info-color);
    color: white;
}

.cache-btn.clear-docs {
    background: var(--accent-color);
    color: white;
}

.cache-btn.clear-hot {
    background: var(--success-color);
    color: white;
}

.cache-btn.clear-all {
    background: var(--error-color);
    color: white;
}

.cache-preload {
    margin-bottom: 20px;
}

.cache-preload h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-size: 16px;
}

.preload-form {
    display: flex;
    gap: 10px;
}

.preload-form input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
}

.cache-info h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-size: 16px;
}

.info-content {
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid var(--border-color);
}

.info-content p {
    margin: 0 0 10px 0;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-color);
}

.info-content p:last-child {
    margin-bottom: 0;
}

.cache-hit-rate {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.cache-hit-rate.high {
    background: var(--success-color);
    color: white;
}

.cache-hit-rate.medium {
    background: var(--warning-color);
    color: white;
}

.cache-hit-rate.low {
    background: var(--error-color);
    color: white;
}

/* 加载状态 */
.cache-loading {
    text-align: center;
    padding: 20px;
    color: var(--subtle-text-color);
}

.cache-loading .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cache-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .cache-buttons {
        grid-template-columns: 1fr;
    }
    
    .preload-form {
        flex-direction: column;
    }
}