#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA信息Web应用 (重构版)
提供一个简洁的API，用于实时获取和搜索OA信息。
"""

import sys
import os
import logging
import base64
from functools import wraps
from flask import Flask, render_template, jsonify, request, Response
from flask_cors import CORS

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oa_scraper import OAScraper
from favorites_manager import FavoritesManager
from cache_manager import get_cache_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('web_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
CORS(app)

# 密码保护配置
USERNAME = "admin"
PASSWORD = "STU2025STU"

def check_auth(username, password):
    """检查用户名和密码"""
    return username == USERNAME and password == PASSWORD

def authenticate():
    """发送认证请求"""
    return Response(
        'Could not verify your access level for that URL.\n'
        'You have to login with proper credentials', 401,
        {'WWW-Authenticate': 'Basic realm="Login Required"'})

def requires_auth(f):
    """认证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        auth = request.authorization
        if not auth or not check_auth(auth.username, auth.password):
            return authenticate()
        return f(*args, **kwargs)
    return decorated

# 全局变量存储OA爬虫实例
oa_scraper = None
# 全局变量存储收藏夹管理器
favorites_manager = None
# 全局变量存储缓存管理器
cache_mgr = None

def get_oa_scraper():
    """获取OA爬虫实例（单例模式）"""
    global oa_scraper
    if oa_scraper is None:
        try:
            oa_scraper = OAScraper()
            logging.info("OA爬虫实例初始化成功")
        except Exception as e:
            logging.error(f"OA爬虫实例初始化失败: {e}")
            raise
    return oa_scraper

def get_favorites_manager():
    """获取收藏夹管理器实例（单例模式）"""
    global favorites_manager
    if favorites_manager is None:
        try:
            favorites_manager = FavoritesManager()
            logging.info("收藏夹管理器初始化成功")
        except Exception as e:
            logging.error(f"收藏夹管理器初始化失败: {e}")
            raise
    return favorites_manager

def get_cache_manager_instance():
    """获取缓存管理器实例（单例模式）"""
    global cache_mgr
    if cache_mgr is None:
        try:
            cache_mgr = get_cache_manager()
            logging.info("缓存管理器初始化成功")
        except Exception as e:
            logging.error(f"缓存管理器初始化失败: {e}")
            raise
    return cache_mgr

@app.route('/')
@requires_auth
def index():
    """主页，提供新的UI"""
    return render_template('index.html')

@app.route('/api/get_oa_data', methods=['GET'])
@requires_auth
def get_oa_data():
    """代理API，用于从OA服务器获取数据，支持分页和搜索。"""
    try:
        # 获取分页和搜索参数
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        keyword = request.args.get('keyword', '', type=str)
        use_cache = request.args.get('cache', 'true', type=str).lower() == 'true'
        
        # 获取高级搜索参数
        unit = request.args.get('unit', '', type=str)
        dept = request.args.get('dept', '', type=str)
        start_date = request.args.get('startDate', '', type=str)
        end_date = request.args.get('endDate', '', type=str)
        author = request.args.get('author', '', type=str)
        min_length = request.args.get('minLength', '', type=str)
        max_length = request.args.get('maxLength', '', type=str)

        # 参数验证
        if page < 1:
            page = 1
        if not (1 <= limit <= 100):
            return jsonify({'error': 'limit参数必须在1到100之间'}), 400

        # 构建搜索参数
        search_params = {
            'page': page,
            'limit': limit,
            'keyword': keyword,
            'unit': unit,
            'dept': dept,
            'start_date': start_date,
            'end_date': end_date,
            'author': author,
            'min_length': min_length,
            'max_length': max_length
        }

        logging.info(f"收到数据请求: {search_params}")

        # 尝试从缓存获取结果
        if use_cache:
            cache_mgr = get_cache_manager_instance()
            cached_result = cache_mgr.get_cached_search(keyword, search_params)
            if cached_result:
                logging.info(f"从缓存返回 {len(cached_result['data'])} 条OA记录")
                # 添加缓存标识
                cached_result['from_cache'] = True
                return jsonify(cached_result)

        # 缓存未命中，从原始数据源获取
        scraper = get_oa_scraper()
        result = scraper.enhanced_search(**search_params)

        if result is None or 'data' not in result or 'total' not in result:
            logging.error("从OAScraper获取数据失败或格式不正确")
            return jsonify({'error': '获取OA信息失败，请稍后重试'}), 500

        # 缓存结果
        if use_cache and keyword:  # 只缓存有关键词的搜索
            cache_mgr.cache_search_result(keyword, search_params, result)

        logging.info(f"成功返回 {len(result['data'])} 条OA记录，总计 {result['total']} 条")
        result['from_cache'] = False
        return jsonify(result)

    except Exception as e:
        logging.error(f"处理API请求时发生严重错误: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500


@app.route('/api/search_suggestions', methods=['GET'])
@requires_auth
def get_search_suggestions():
    """获取搜索建议"""
    try:
        query = request.args.get('q', '', type=str)
        if not query or len(query) < 2:
            return jsonify([])
        
        # 尝试从缓存获取
        cache_mgr = get_cache_manager_instance()
        cache_key = f"suggestions_{query}"
        cached_suggestions = cache_mgr.get_hot_data(cache_key)
        if cached_suggestions:
            return jsonify(cached_suggestions)
        
        # 从原始数据源获取
        scraper = get_oa_scraper()
        suggestions = scraper.get_search_suggestions(query)
        
        # 缓存结果
        cache_mgr.cache_hot_data(cache_key, suggestions)
        
        return jsonify(suggestions)
    except Exception as e:
        logging.error(f"获取搜索建议失败: {e}")
        return jsonify([])


@app.route('/api/popular_keywords', methods=['GET'])
@requires_auth
def get_popular_keywords():
    """获取热门关键词"""
    try:
        # 尝试从缓存获取
        cache_mgr = get_cache_manager_instance()
        cached_keywords = cache_mgr.get_hot_data('popular_keywords')
        if cached_keywords:
            return jsonify(cached_keywords)
        
        # 从原始数据源获取
        scraper = get_oa_scraper()
        keywords = scraper.get_popular_keywords()
        
        # 缓存结果
        cache_mgr.cache_hot_data('popular_keywords', keywords)
        
        return jsonify(keywords)
    except Exception as e:
        logging.error(f"获取热门关键词失败: {e}")
        return jsonify([])


@app.route('/api/filter_options', methods=['GET'])
@requires_auth
def get_filter_options():
    """获取过滤器选项"""
    try:
        # 尝试从缓存获取
        cache_mgr = get_cache_manager_instance()
        cached_options = cache_mgr.get_hot_data('filter_options')
        if cached_options:
            return jsonify(cached_options)
        
        # 从原始数据源获取
        scraper = get_oa_scraper()
        options = scraper.get_filter_options()
        
        # 缓存结果
        cache_mgr.cache_hot_data('filter_options', options)
        
        return jsonify(options)
    except Exception as e:
        logging.error(f"获取过滤器选项失败: {e}")
        return jsonify({'units': [], 'departments': [], 'authors': []})

# 收藏夹相关API
@app.route('/api/favorites', methods=['GET'])
@requires_auth
def get_favorites():
    """获取收藏列表"""
    try:
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        keyword = request.args.get('keyword', '', type=str)
        tag = request.args.get('tag', '', type=str)
        
        if page < 1:
            page = 1
        if not (1 <= limit <= 100):
            return jsonify({'error': 'limit参数必须在1到100之间'}), 400
        
        offset = (page - 1) * limit
        favorites_mgr = get_favorites_manager()
        
        if tag:
            result = favorites_mgr.get_favorites_by_tag(tag, limit, offset)
        elif keyword:
            result = favorites_mgr.search_favorites(keyword, limit, offset)
        else:
            result = favorites_mgr.get_favorites(limit, offset)
        
        return jsonify(result)
    except Exception as e:
        logging.error(f"获取收藏列表失败: {e}")
        return jsonify({'favorites': [], 'total': 0, 'error': str(e)}), 500

@app.route('/api/favorites', methods=['POST'])
@requires_auth
def add_favorite():
    """添加收藏"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        favorites_mgr = get_favorites_manager()
        success = favorites_mgr.add_favorite(data)
        
        if success:
            return jsonify({'success': True, 'message': '添加收藏成功'})
        else:
            return jsonify({'success': False, 'message': '添加收藏失败，可能已经收藏'}), 400
    except Exception as e:
        logging.error(f"添加收藏失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/favorites/<doc_id>', methods=['GET'])
@requires_auth
def get_favorite(doc_id):
    """获取单个收藏详情"""
    try:
        favorites_mgr = get_favorites_manager()
        favorites = favorites_mgr.get_favorites(limit=1000)  # 获取较多收藏来查找
        
        # 查找指定的收藏
        target_favorite = None
        for fav in favorites['favorites']:
            if fav.get('id') == doc_id:
                target_favorite = fav
                break
        
        if target_favorite:
            return jsonify(target_favorite)
        else:
            return jsonify({'error': '收藏不存在'}), 404
    except Exception as e:
        logging.error(f"获取收藏详情失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/favorites/<doc_id>', methods=['DELETE'])
@requires_auth
def remove_favorite(doc_id):
    """移除收藏"""
    try:
        favorites_mgr = get_favorites_manager()
        success = favorites_mgr.remove_favorite(doc_id)
        
        if success:
            return jsonify({'success': True, 'message': '移除收藏成功'})
        else:
            return jsonify({'success': False, 'message': '移除收藏失败，收藏不存在'}), 404
    except Exception as e:
        logging.error(f"移除收藏失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/favorites/<doc_id>/check', methods=['GET'])
@requires_auth
def check_favorite(doc_id):
    """检查是否已收藏"""
    try:
        favorites_mgr = get_favorites_manager()
        is_fav = favorites_mgr.is_favorite(doc_id)
        
        return jsonify({'is_favorite': is_fav})
    except Exception as e:
        logging.error(f"检查收藏状态失败: {e}")
        return jsonify({'is_favorite': False, 'error': str(e)}), 500

@app.route('/api/favorites/<doc_id>/tags', methods=['POST'])
@requires_auth
def add_tag(doc_id):
    """为收藏项添加标签"""
    try:
        data = request.get_json()
        tag = data.get('tag', '').strip()
        
        if not tag:
            return jsonify({'error': '标签不能为空'}), 400
        
        favorites_mgr = get_favorites_manager()
        success = favorites_mgr.add_tag(doc_id, tag)
        
        if success:
            return jsonify({'success': True, 'message': '添加标签成功'})
        else:
            return jsonify({'success': False, 'message': '添加标签失败'}), 400
    except Exception as e:
        logging.error(f"添加标签失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/favorites/<doc_id>/tags/<tag>', methods=['DELETE'])
@requires_auth
def remove_tag(doc_id, tag):
    """移除收藏项的标签"""
    try:
        favorites_mgr = get_favorites_manager()
        success = favorites_mgr.remove_tag(doc_id, tag)
        
        if success:
            return jsonify({'success': True, 'message': '移除标签成功'})
        else:
            return jsonify({'success': False, 'message': '移除标签失败'}), 400
    except Exception as e:
        logging.error(f"移除标签失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/favorites/tags', methods=['GET'])
@requires_auth
def get_tags():
    """获取所有标签"""
    try:
        favorites_mgr = get_favorites_manager()
        tags = favorites_mgr.get_tags()
        
        return jsonify({'tags': tags})
    except Exception as e:
        logging.error(f"获取标签失败: {e}")
        return jsonify({'tags': [], 'error': str(e)}), 500

# 缓存管理相关API
@app.route('/api/cache/stats', methods=['GET'])
@requires_auth
def get_cache_stats():
    """获取缓存统计信息"""
    try:
        cache_mgr = get_cache_manager_instance()
        stats = cache_mgr.get_cache_stats()
        return jsonify(stats)
    except Exception as e:
        logging.error(f"获取缓存统计失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/cache/clear', methods=['POST'])
@requires_auth
def clear_cache():
    """清理缓存"""
    try:
        data = request.get_json() or {}
        cache_type = data.get('type', 'all')  # all, expired, search, documents, hot_data
        
        cache_mgr = get_cache_manager_instance()
        
        if cache_type == 'all':
            cache_mgr.clear_all_cache()
            message = '所有缓存已清理'
        elif cache_type == 'expired':
            cache_mgr.clear_expired_cache()
            message = '过期缓存已清理'
        elif cache_type == 'search':
            cache_mgr.search_cache.clear()
            message = '搜索缓存已清理'
        elif cache_type == 'documents':
            cache_mgr.document_cache.clear()
            message = '文档缓存已清理'
        elif cache_type == 'hot_data':
            cache_mgr.hot_data_cache.clear()
            message = '热门数据缓存已清理'
        else:
            return jsonify({'error': '不支持的缓存类型'}), 400
        
        return jsonify({'success': True, 'message': message})
    except Exception as e:
        logging.error(f"清理缓存失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/cache/preload', methods=['POST'])
@requires_auth
def preload_cache():
    """预加载缓存"""
    try:
        data = request.get_json() or {}
        keywords = data.get('keywords', [])
        filters = data.get('filters', {})
        
        if not keywords:
            return jsonify({'error': '请提供关键词列表'}), 400
        
        cache_mgr = get_cache_manager_instance()
        cache_mgr.preload_search_results(keywords, filters)
        
        return jsonify({'success': True, 'message': f'已标记 {len(keywords)} 个关键词进行预加载'})
    except Exception as e:
        logging.error(f"预加载缓存失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/cache/document/<doc_id>', methods=['GET'])
@requires_auth
def get_cached_document(doc_id):
    """获取缓存的文档详情"""
    try:
        cache_mgr = get_cache_manager_instance()
        document = cache_mgr.get_cached_document(doc_id)
        
        if document:
            return jsonify({
                'success': True,
                'document': document,
                'from_cache': True
            })
        else:
            return jsonify({
                'success': False,
                'message': '文档未缓存'
            }), 404
    except Exception as e:
        logging.error(f"获取缓存文档失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': '请求的资源不存在'}), 404

if __name__ == '__main__':
    try:
        # 初始化爬虫实例
        get_oa_scraper()
        
        # 生产环境使用gunicorn，这里仅用于本地测试
        if os.environ.get('FLASK_ENV') == 'production':
            port = int(os.environ.get('PORT', 10000))
            logging.info(f"启动OA信息Web应用 (生产环境)...")
            logging.info(f"端口: {port}")
            app.run(
                host='0.0.0.0',
                port=port,
                debug=False,
                threaded=True
            )
        else:
            logging.info("启动OA信息Web应用 (开发环境)...")
            logging.info("请访问: http://localhost:5035")
            app.run(
                host='0.0.0.0',
                port=5035,
                debug=True,
                threaded=True
            )
        
    except KeyboardInterrupt:
        logging.info("用户中断，正在关闭应用...")
    except Exception as e:
        logging.error(f"应用启动失败: {e}")
        sys.exit(1)