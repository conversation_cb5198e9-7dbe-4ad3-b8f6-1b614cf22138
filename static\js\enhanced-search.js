class EnhancedSearch {
    constructor() {
        this.searchHistory = this.loadSearchHistory();
        this.currentFilters = {};
        this.searchMode = 'basic';
        this.isLoading = false;
        this.debounceTimer = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateSearchMode();
    }

    bindEvents() {
        const keywordInput = document.getElementById('keywordInput');
        const searchBtn = document.getElementById('searchBtn');
        const advancedToggle = document.getElementById('advancedToggle');
        const searchTabs = document.querySelectorAll('.search-tab');
        const historyToggle = document.getElementById('historyToggle');

        // 搜索输入事件
        if (keywordInput) {
            keywordInput.addEventListener('input', (e) => {
                clearTimeout(this.debounceTimer);
                this.debounceTimer = setTimeout(() => {
                    this.handleSearchInput(e.target.value);
                }, 300);
            });
        }

        // 搜索按钮事件
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }

        // 高级搜索切换
        if (advancedToggle) {
            advancedToggle.addEventListener('click', () => {
                this.toggleAdvancedSearch();
            });
        }

        // 搜索标签切换
        searchTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const mode = e.target.dataset.mode;
                if (mode) {
                    this.setSearchMode(mode);
                }
            });
        });

        // 历史记录切换
        if (historyToggle) {
            historyToggle.addEventListener('click', () => {
                this.toggleSearchHistory();
            });
        }

        // 高级搜索表单事件
        this.bindAdvancedSearchEvents();
    }

    bindAdvancedSearchEvents() {
        const advancedInputs = document.querySelectorAll('.advanced-search-panel input, .advanced-search-panel select');
        advancedInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateFilters();
            });
        });
    }

    setSearchMode(mode) {
        this.searchMode = mode;
        this.updateSearchMode();
    }

    updateSearchMode() {
        const basicTab = document.querySelector('[data-mode="basic"]');
        const advancedTab = document.querySelector('[data-mode="advanced"]');
        const historyTab = document.querySelector('[data-mode="history"]');
        const favoritesTab = document.querySelector('[data-mode="favorites"]');
        const cacheTab = document.querySelector('[data-mode="cache"]');
        const searchContainer = document.querySelector('.search-container');
        const advancedPanel = document.querySelector('.advanced-search-panel');
        const historyPanel = document.querySelector('.search-history-panel');
        const favoritesPanel = document.querySelector('.favorites-panel');
        const cachePanel = document.querySelector('.cache-panel');

        // 更新标签激活状态
        document.querySelectorAll('.search-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // 根据模式显示/隐藏面板
        switch(this.searchMode) {
            case 'basic':
                if (basicTab) basicTab.classList.add('active');
                if (searchContainer) searchContainer.style.display = 'block';
                if (advancedPanel) advancedPanel.style.display = 'none';
                if (historyPanel) historyPanel.style.display = 'none';
                if (favoritesPanel) favoritesPanel.style.display = 'none';
                break;
            case 'advanced':
                if (advancedTab) advancedTab.classList.add('active');
                if (searchContainer) searchContainer.style.display = 'block';
                if (advancedPanel) advancedPanel.style.display = 'block';
                if (historyPanel) historyPanel.style.display = 'none';
                if (favoritesPanel) favoritesPanel.style.display = 'none';
                if (cachePanel) cachePanel.style.display = 'none';
                break;
            case 'history':
                if (historyTab) historyTab.classList.add('active');
                if (searchContainer) searchContainer.style.display = 'none';
                if (advancedPanel) advancedPanel.style.display = 'none';
                if (historyPanel) {
                    historyPanel.style.display = 'block';
                    this.renderSearchHistory();
                }
                if (favoritesPanel) favoritesPanel.style.display = 'none';
                break;
            case 'favorites':
                if (favoritesTab) favoritesTab.classList.add('active');
                if (searchContainer) searchContainer.style.display = 'none';
                if (advancedPanel) advancedPanel.style.display = 'none';
                if (historyPanel) historyPanel.style.display = 'none';
                if (favoritesPanel) {
                    favoritesPanel.style.display = 'block';
                    // 触发收藏夹管理器加载
                    if (window.favoritesManager) {
                        window.favoritesManager.showFavoritesPanel();
                    }
                }
                break;
            case 'cache':
                if (cacheTab) cacheTab.classList.add('active');
                if (searchContainer) searchContainer.style.display = 'none';
                if (advancedPanel) advancedPanel.style.display = 'none';
                if (historyPanel) historyPanel.style.display = 'none';
                if (favoritesPanel) favoritesPanel.style.display = 'none';
                if (cachePanel) {
                    cachePanel.style.display = 'block';
                    // 触发缓存管理器加载
                    if (window.cacheManager) {
                        window.cacheManager.showCachePanel();
                    }
                }
                break;
        }
    }

    toggleAdvancedSearch() {
        if (this.searchMode === 'advanced') {
            this.setSearchMode('basic');
        } else {
            this.setSearchMode('advanced');
        }
    }

    toggleSearchHistory() {
        if (this.searchMode === 'history') {
            this.setSearchMode('basic');
        } else {
            this.setSearchMode('history');
        }
    }

    handleSearchInput(value) {
        if (value.length > 2) {
            this.fetchSearchSuggestions(value);
        }
    }

    async fetchSearchSuggestions(keyword) {
        try {
            const response = await fetch(`/api/search_suggestions?keyword=${encodeURIComponent(keyword)}`);
            if (response.ok) {
                const suggestions = await response.json();
                this.displaySuggestions(suggestions);
            }
        } catch (error) {
            console.error('Failed to fetch suggestions:', error);
        }
    }

    displaySuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (!suggestionsContainer) return;

        if (suggestions.length === 0) {
            suggestionsContainer.style.display = 'none';
            return;
        }

        suggestionsContainer.innerHTML = suggestions.map(item => `
            <div class="suggestion-item" data-value="${item}">
                ${item}
            </div>
        `).join('');

        suggestionsContainer.style.display = 'block';

        // 绑定建议项点击事件
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const value = e.target.dataset.value;
                document.getElementById('keywordInput').value = value;
                suggestionsContainer.style.display = 'none';
                this.performSearch();
            });
        });
    }

    updateFilters() {
        this.currentFilters = {};
        
        // 收集所有高级搜索过滤器
        const unit = document.getElementById('unitFilter')?.value;
        const dept = document.getElementById('deptFilter')?.value;
        const startDate = document.getElementById('startDate')?.value;
        const endDate = document.getElementById('endDate')?.value;
        const author = document.getElementById('authorFilter')?.value;
        const minLength = document.getElementById('minLength')?.value;
        const maxLength = document.getElementById('maxLength')?.value;

        if (unit) this.currentFilters.unit = unit;
        if (dept) this.currentFilters.dept = dept;
        if (startDate) this.currentFilters.startDate = startDate;
        if (endDate) this.currentFilters.endDate = endDate;
        if (author) this.currentFilters.author = author;
        if (minLength) this.currentFilters.minLength = minLength;
        if (maxLength) this.currentFilters.maxLength = maxLength;
    }

    async performSearch() {
        const keyword = document.getElementById('keywordInput')?.value || '';
        
        if (!keyword.trim() && Object.keys(this.currentFilters).length === 0) {
            return;
        }

        // 添加到搜索历史
        if (keyword.trim()) {
            this.addToSearchHistory(keyword);
        }

        // 更新过滤器
        this.updateFilters();

        // 使用全局的fetchData函数
        if (typeof window.fetchData === 'function') {
            window.fetchData(1, keyword, true, this.currentFilters);
        } else {
            // 回退到原来的方法
            await this.fallbackSearch(keyword);
        }
    }

    async fallbackSearch(keyword) {
        // 构建搜索参数
        const params = new URLSearchParams();
        params.append('keyword', keyword);
        params.append('page', '1');
        params.append('limit', '20');

        // 添加高级搜索过滤器
        if (this.searchMode === 'advanced') {
            Object.entries(this.currentFilters).forEach(([key, value]) => {
                params.append(key, value);
            });
        }

        // 执行搜索
        try {
            this.isLoading = true;
            this.showLoadingState();

            const response = await fetch(`/api/get_oa_data?${params.toString()}`);
            if (response.ok) {
                const data = await response.json();
                this.displaySearchResults(data);
            }
        } catch (error) {
            console.error('Search failed:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    displaySearchResults(data) {
        // 调用全局的 renderData 函数
        if (typeof window.renderData === 'function') {
            window.renderData(data.items || []);
        }

        // 更新结果计数
        const resultCount = document.getElementById('resultCount');
        if (resultCount) {
            resultCount.textContent = `找到 ${data.total || 0} 条结果`;
        }
    }

    addToSearchHistory(keyword) {
        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item !== keyword);
        
        // 添加到开头
        this.searchHistory.unshift(keyword);
        
        // 限制历史记录数量
        if (this.searchHistory.length > 10) {
            this.searchHistory = this.searchHistory.slice(0, 10);
        }

        // 保存到本地存储
        this.saveSearchHistory();
    }

    renderSearchHistory() {
        const historyPanel = document.querySelector('.search-history-panel');
        if (!historyPanel) return;

        if (this.searchHistory.length === 0) {
            historyPanel.innerHTML = '<p class="no-history">暂无搜索历史</p>';
            return;
        }

        historyPanel.innerHTML = `
            <div class="history-header">
                <h3>搜索历史</h3>
                <button class="clear-history-btn" onclick="window.enhancedSearch.clearSearchHistory()">清空历史</button>
            </div>
            <div class="history-items">
                ${this.searchHistory.map((item, index) => `
                    <div class="history-item" data-index="${index}">
                        <span class="history-text">${item}</span>
                        <button class="history-use-btn" onclick="window.enhancedSearch.useHistoryItem('${item}')">使用</button>
                        <button class="history-delete-btn" onclick="window.enhancedSearch.deleteHistoryItem(${index})">删除</button>
                    </div>
                `).join('')}
            </div>
        `;
    }

    useHistoryItem(keyword) {
        const keywordInput = document.getElementById('keywordInput');
        if (keywordInput) {
            keywordInput.value = keyword;
            this.setSearchMode('basic');
            this.performSearch();
        }
    }

    deleteHistoryItem(index) {
        this.searchHistory.splice(index, 1);
        this.saveSearchHistory();
        this.renderSearchHistory();
    }

    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.renderSearchHistory();
    }

    saveSearchHistory() {
        try {
            localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.error('Failed to save search history:', error);
        }
    }

    loadSearchHistory() {
        try {
            const history = localStorage.getItem('searchHistory');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('Failed to load search history:', error);
            return [];
        }
    }

    showLoadingState() {
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.disabled = true;
            searchBtn.textContent = '搜索中...';
        }
    }

    hideLoadingState() {
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.disabled = false;
            searchBtn.textContent = '搜索';
        }
    }

    showErrorState() {
        const resultContainer = document.getElementById('dataContainer');
        if (resultContainer) {
            resultContainer.innerHTML = '<p class="error-message">搜索失败，请稍后重试</p>';
        }
    }
}

// 初始化增强搜索
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedSearch = new EnhancedSearch();
});
