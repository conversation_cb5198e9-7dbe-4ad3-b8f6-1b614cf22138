<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OA 信息查询系统</title>
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #f5f5f5;
            --text-color: #333;
            --subtle-text-color: #757575;
            --border-color: #e0e0e0;
            --shadow-color: rgba(0, 0, 0, 0.08);
            --accent-color: #2196f3;
            --highlight-color: #bbdefb;
            --success-color: #4caf50;
            --error-color: #f44336;
        }
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: var(--text-color);
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 10px 0;
            border-bottom: 1px solid var(--border-color);
        }
        h1 {
            color: var(--primary-color);
            margin: 0;
            padding: 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        .search-container {
            display: flex;
            margin-bottom: 30px;
            gap: 12px;
        }
        .search-input {
            flex: 1;
            padding: 12px 18px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 16px;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.2s ease;
        }
        .search-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px var(--highlight-color);
        }
        .search-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.2s ease;
        }
        .search-button:hover {
            background-color: #1565c0;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }
        .search-button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px var(--shadow-color);
        }
        .data-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .data-item {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 3px 6px var(--shadow-color);
            padding: 20px;
            border-left: 5px solid var(--primary-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .data-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        .data-item h2 {
            margin-top: 0;
            margin-bottom: 14px;
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
            line-height: 1.4;
        }
        .meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            color: var(--subtle-text-color);
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
            padding-top: 12px;
            margin-top: 8px;
        }
        .meta span {
            display: flex;
            align-items: center;
        }
        .meta span::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: var(--accent-color);
            border-radius: 50%;
            margin-right: 8px;
        }
        .loader {
            text-align: center;
            padding: 20px;
            color: var(--subtle-text-color);
            display: none;
            font-style: italic;
        }
        .error-message {
            background-color: #ffebee;
            color: var(--error-color);
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 25px;
            display: none;
            border-left: 4px solid var(--error-color);
            font-weight: 500;
        }
        
        /* View more link styles */
        .view-more {
            color: var(--accent-color);
            margin-top: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
            padding: 5px 0;
            transition: color 0.2s ease;
        }
        
        .view-more:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }
        
        /* Modal styles */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 100;
            overflow-y: auto;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 8px;
            max-width: 800px;
            width: 90%;
            margin: 30px auto;
            padding: 25px;
            position: relative;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .modal-header {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        
        .modal-title {
            margin: 0;
            font-size: 22px;
            color: var(--primary-color);
        }
        
        .modal-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
            color: var(--subtle-text-color);
            font-size: 14px;
        }
        
        .modal-meta-item {
            display: flex;
            align-items: center;
        }
        
        .modal-meta-item::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            background-color: var(--accent-color);
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .modal-body {
            line-height: 1.7;
            margin-bottom: 20px;
            max-height: 70vh;
            overflow-y: auto;
            padding-right: 5px;
        }
        
        .standardized-content {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-color);
            text-align: left;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .standardized-content p {
            margin-bottom: 1em;
        }
        
        .standardized-content h1, .standardized-content h2, .standardized-content h3 {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            color: var(--primary-color);
        }
        
        /* Override any inline styles that might have survived cleaning */
        .standardized-content * {
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
        }
        
        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            background-color: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .modal-close:hover {
            background-color: var(--border-color);
        }
        
        .modal-close::before, .modal-close::after {
            content: '';
            position: absolute;
            width: 15px;
            height: 2px;
            background-color: var(--subtle-text-color);
        }
        
        .modal-close::before {
            transform: rotate(45deg);
        }
        
        .modal-close::after {
            transform: rotate(-45deg);
        }
    </style>
    <script>
// 全局清理函数将由 text-cleaner-enhanced.js 提供
// 这里只保留一个兼容性函数作为备用
window.fallbackCleanHtml = function(content) {
    if (!content) return '';
    let text = content.toString();
    // 基本的HTML清理
    text = text.replace(/<[^>]*>/g, '');
    text = text.replace(/&nbsp;/g, ' ');
    text = text.replace(/\s+/g, ' ');
    return text.trim() || '无内容';
};
</script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>OA 信息查询系统</h1>
        </div>
        
        <!-- 增强的搜索区域 -->
        <!-- 增强的搜索区域 -->
        <div class="search-enhanced">
            <div class="search-tabs">
                <button class="search-tab active" data-mode="basic">
                    <i class="fas fa-search"></i> 基础搜索
                </button>
                <button class="search-tab" data-mode="advanced">
                    <i class="fas fa-cog"></i> 高级搜索
                </button>
                <button class="search-tab" data-mode="history">
                    <i class="fas fa-history"></i> 搜索历史
                </button>
                <button class="search-tab" data-mode="favorites">
                    <i class="fas fa-star"></i> 收藏夹
                </button>
                <button class="search-tab" data-mode="cache">
                    <i class="fas fa-database"></i> 缓存管理
                </button>
            </div>
            
            <div class="search-form">
                <div class="search-input-group">
                    <input type="text" id="keywordInput" class="search-input" placeholder="输入关键词搜索...">
                    <i class="fas fa-search search-icon"></i>
                    <div class="search-suggestions" id="searchSuggestions"></div>
                </div>
                <button id="searchBtn" class="search-button">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <button id="advancedToggle" class="advanced-search-toggle">
                    <i class="fas fa-sliders-h"></i> 高级选项
                </button>
            </div>
            
            <div class="advanced-search-panel" id="advancedPanel">
                <div class="form-group">
                    <label class="form-label">发布单位</label>
                    <select class="form-select" id="unitFilter">
                        <option value="">全部单位</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">发布部门</label>
                    <select class="form-select" id="deptFilter">
                        <option value="">全部部门</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="form-group">
                    <label class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="form-group">
                    <label class="form-label">发布人</label>
                    <input type="text" class="form-control" id="authorFilter" placeholder="输入发布人姓名">
                </div>
                <div class="form-group">
                    <label class="form-label">内容长度</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="number" class="form-control" id="minLength" placeholder="最小值">
                        <input type="number" class="form-control" id="maxLength" placeholder="最大值">
                    </div>
                </div>
            </div>
            
            <div class="search-filters" id="searchFilters"></div>
            
            <div class="search-history-panel" id="searchHistoryPanel" style="display: none;">
                <div class="search-history-title">
                    <i class="fas fa-history"></i> 搜索历史
                </div>
                <div class="search-history-tags" id="historyTags"></div>
            </div>
            
            <div class="favorites-panel" id="favoritesPanel" style="display: none;">
                <div class="favorites-header">
                    <h3 class="favorites-title">
                        <i class="fas fa-star"></i> 收藏夹
                    </h3>
                    <div class="favorites-stats" id="favoritesStats">
                        共 <span id="favoritesCount">0</span> 条收藏
                    </div>
                </div>
                
                <div class="favorites-search">
                    <input type="text" id="favoritesSearch" placeholder="搜索收藏内容...">
                    <button id="favoritesSearchBtn">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                
                <div class="favorites-tags" id="favoritesTags">
                    <!-- 标签将动态生成 -->
                </div>
                
                <div class="favorites-content" id="favoritesContent">
                    <!-- 收藏内容将动态生成 -->
                </div>
                
                <div class="favorites-pagination" id="favoritesPagination" style="display: none;">
                    <!-- 分页将动态生成 -->
                </div>
            </div>
            
            <div class="cache-panel" id="cachePanel" style="display: none;">
                <div class="cache-header">
                    <h3 class="cache-title">
                        <i class="fas fa-database"></i> 缓存管理
                    </h3>
                    <div class="cache-actions">
                        <button class="cache-btn" onclick="refreshCacheStats()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>
                
                <div class="cache-stats" id="cacheStats">
                    <!-- 缓存统计信息将动态生成 -->
                </div>
                
                <div class="cache-controls">
                    <h4>缓存操作</h4>
                    <div class="cache-buttons">
                        <button class="cache-btn clear-expired" onclick="clearCache('expired')">
                            <i class="fas fa-broom"></i> 清理过期缓存
                        </button>
                        <button class="cache-btn clear-search" onclick="clearCache('search')">
                            <i class="fas fa-search"></i> 清理搜索缓存
                        </button>
                        <button class="cache-btn clear-docs" onclick="clearCache('documents')">
                            <i class="fas fa-file"></i> 清理文档缓存
                        </button>
                        <button class="cache-btn clear-hot" onclick="clearCache('hot_data')">
                            <i class="fas fa-fire"></i> 清理热门数据
                        </button>
                        <button class="cache-btn clear-all" onclick="clearCache('all')">
                            <i class="fas fa-trash"></i> 清理所有缓存
                        </button>
                    </div>
                </div>
                
                <div class="cache-preload">
                    <h4>预加载缓存</h4>
                    <div class="preload-form">
                        <input type="text" id="preloadKeywords" placeholder="输入关键词，用逗号分隔...">
                        <button class="cache-btn" onclick="preloadCache()">
                            <i class="fas fa-download"></i> 预加载
                        </button>
                    </div>
                </div>
                
                <div class="cache-info">
                    <h4>缓存说明</h4>
                    <div class="info-content">
                        <p><strong>内存缓存：</strong>存储在内存中，访问速度最快，重启后丢失</p>
                        <p><strong>磁盘缓存：</strong>存储在硬盘上，访问速度快，重启后仍然存在</p>
                        <p><strong>搜索缓存：</strong>缓存搜索结果，TTL为5分钟</p>
                        <p><strong>文档缓存：</strong>缓存文档详情，TTL为30分钟</p>
                        <p><strong>热门数据：</strong>缓存热门关键词和过滤器选项，TTL为10分钟</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="errorMessage" class="error-message"></div>
        
        <div id="dataContainer" class="data-container"></div>
        
        <div id="loader" class="loader">正在加载数据...</div>
    </div>
    
    <!-- 标签输入模态框 -->
    <div class="tag-input-modal" id="tagInputModal">
        <div class="tag-input-content">
            <div class="tag-input-header">
                <h4 class="tag-input-title">添加标签</h4>
                <button class="tag-input-close" id="tagInputClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <input type="text" class="tag-input-field" id="tagInputField" placeholder="请输入标签名称...">
            <div class="tag-input-actions">
                <button class="tag-input-cancel" id="tagInputCancel">取消</button>
                <button class="tag-input-save" id="tagInputSave">保存</button>
            </div>
        </div>
    </div>
    
    <!-- Modal for full content view -->
    <div id="contentModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-close" id="closeModal"></div>
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle"></h2>
            </div>
            <div class="modal-meta" id="modalMeta">
                <!-- Metadata will be inserted here -->
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Content will be inserted here -->
            </div>
        </div>
    </div>

    <!-- 增强的搜索功能 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <!-- 增强的文本清理工具 -->
    <script src="{{ url_for('static', filename='js/text-cleaner-enhanced.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const dataContainer = document.getElementById('dataContainer');
            const searchInput = document.getElementById('keywordInput');
            const searchButton = document.getElementById('searchBtn');
            const loader = document.getElementById('loader');
            const errorMessage = document.getElementById('errorMessage');

            let currentPage = 1;
            const limit = 20;
            let keyword = '';
            let isLoading = false;
            let hasMore = true;

            // 暴露给全局的函数，供增强搜索使用
            window.fetchData = async (page = currentPage, searchKeyword = keyword, reset = false, filters = {}) => {
                if (isLoading || !hasMore) return;
                
                isLoading = true;
                loader.style.display = 'block';
                errorMessage.style.display = 'none';
                errorMessage.textContent = '';

                try {
                    // 构建请求参数
                    const params = new URLSearchParams();
                    params.append('page', page);
                    params.append('limit', limit);
                    params.append('keyword', searchKeyword);
                    
                    // 添加高级搜索过滤器
                    if (filters.unit) params.append('unit', filters.unit);
                    if (filters.dept) params.append('dept', filters.dept);
                    if (filters.startDate) params.append('startDate', filters.startDate);
                    if (filters.endDate) params.append('endDate', filters.endDate);
                    if (filters.author) params.append('author', filters.author);
                    if (filters.minLength) params.append('minLength', filters.minLength);
                    if (filters.maxLength) params.append('maxLength', filters.maxLength);
                    
                    const url = `/api/get_oa_data?${params.toString()}`;
                    console.log(`Fetching data from: ${url}`);
                    
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error(`HTTP 错误! 状态: ${response.status}`);
                    }
                    
                    const result = await response.json();
                    if (reset) {
                        dataContainer.innerHTML = '';
                    }

                    renderData(result.data);
                    
                    // Check if there's more data
                    const totalFetched = ((page - 1) * limit) + result.data.length;
                    hasMore = result.total > 0 && totalFetched < result.total;
                    if (!hasMore) {
                         loader.textContent = '没有更多数据了';
                    } else {
                        loader.textContent = '正在加载更多...';
                    }

                } catch (error) {
                    console.error('获取数据失败:', error);
                    errorMessage.textContent = `获取数据失败: ${error.message}`;
                    errorMessage.style.display = 'block';
                    loader.style.display = 'none';
                } finally {
                    isLoading = false;
                    if (hasMore) {
                        loader.style.display = 'none';
                    }
                }
            };

            // 暴露给全局的渲染函数
            window.renderData = (items) => {
                if (!items || items.length === 0) {
                    if (currentPage === 1) {
                        dataContainer.innerHTML = '<p style="text-align:center; color: var(--subtle-text-color);">未找到相关信息。</p>';
                    }
                    return;
                }

                items.forEach(item => {
                    const doc = item.raw_data; // Data is in raw_data
                    if (!doc) return;

                    // Extract title from content or use DOCTITLE
                    let title = doc.DOCTITLE || '';
                    if (!title && doc.DOCCONTENT) {
                        const match = doc.DOCCONTENT.match(/关于.*?的通知|.*?通知|.*?公告|.*?文件/);
                        if (match) {
                            title = match[0];
                        } else {
                            // Use first 50 characters of content as title
                            title = doc.DOCCONTENT.replace(/<[^>]*>/g, '').substring(0, 50) + '...';
                        }
                    }
                    
                    // Clean title with the same approach as modal view
                    if (title) {
                        const originalTitle = title;
                        // 使用增强的标题清理函数
                        title = window.cleanTitle(title);
                        
                        // 检测是否包含乱码并记录
                        if (window.detectGarbledText && window.detectGarbledText(originalTitle)) {
                            console.log(`检测到乱码标题已清理: "${originalTitle.substring(0, 50)}..." -> "${title.substring(0, 50)}..."`);
                        }
                    }
                    
                    title = title || '无标题';

                    // Extract all relevant metadata
                    const unit = doc.SUBCOMPANYNAME || doc.DOCUNIT || '未知';
                    const dept = doc.DEPARTMENTNAME || '未知';
                    const date = doc.DOCVALIDDATE || doc.DOCDATE || '未知';
                    const author = doc.LASTNAME || '未知';
                    const fullContent = doc.DOCCONTENT || '';

                    // Generate a unique ID if DOCID is missing
                    let docId = doc.DOCID || '';
                    if (!docId) {
                        // Create a hash based on title, unit, and date for unique identification
                        const hashSource = `${title}_${unit}_${date}_${author}`;
                        // 使用encodeURIComponent和md5-like hash来避免btoa的Latin1限制
                        let hash = 0;
                        const encoded = encodeURIComponent(hashSource);
                        for (let i = 0; i < encoded.length; i++) {
                            const char = encoded.charCodeAt(i);
                            hash = ((hash << 5) - hash) + char;
                            hash = hash & hash; // Convert to 32-bit integer
                        }
                        docId = 'hash_' + Math.abs(hash).toString(36).substring(0, 16);
                    }

                    const itemElement = document.createElement('div');
                    itemElement.className = 'data-item';
                    // Store full content as data attribute for click event
                    itemElement.dataset.content = fullContent;
                    itemElement.dataset.docId = docId;
                    itemElement.dataset.title = title;
                    itemElement.dataset.unit = unit;
                    itemElement.dataset.dept = dept;
                    itemElement.dataset.author = author;
                    itemElement.dataset.date = date;
                    
                    itemElement.innerHTML = `
                        <h2>${title}</h2>
                        <div class="meta">
                            <span>发布单位: ${unit}</span>
                            <span>发布部门: ${dept}</span>
                            <span>发布人: ${author}</span>
                            <span>发布日期: ${date}</span>
                        </div>
                        <div class="item-actions">
                            <button class="favorite-toggle add" onclick="toggleFavorite(this, event)">
                                <i class="fas fa-star"></i> 收藏
                            </button>
                            <div class="view-more">点击查看全文</div>
                        </div>
                    `;
                    
                    // Add click event listener to view full content
                    itemElement.addEventListener('click', function() {
                        showFullContent(title, fullContent, unit, dept, author, date);
                    });
                    dataContainer.appendChild(itemElement);
                    
                    // 检查收藏状态
                    checkFavoriteStatus(docId, itemElement.querySelector('.favorite-toggle'));
                });
            };

            // 更新搜索按钮事件以使用增强搜索
            searchButton.addEventListener('click', () => {
                const newKeyword = searchInput.value.trim();
                if (keyword === newKeyword && !window.enhancedSearch?.currentFilters) return; // 如果关键词没变且没有过滤器，不重新搜索
                
                dataContainer.innerHTML = ''; // 清空现有数据
                currentPage = 1; // 重置页码
                keyword = newKeyword;
                hasMore = true;
                
                // 使用增强搜索的过滤器
                const filters = window.enhancedSearch?.currentFilters || {};
                
                // 调用全局fetchData函数
                window.fetchData(currentPage, keyword, true, filters);
            });
            
            // 回车键搜索
            searchInput.addEventListener('keyup', (event) => {
                if (event.key === 'Enter') {
                    searchButton.click();
                }
            });

            window.addEventListener('scroll', () => {
                if (isLoading || !hasMore) return;

                if (window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 100) {
                    currentPage++;
                    const filters = window.enhancedSearch?.currentFilters || {};
                    window.fetchData(currentPage, keyword, false, filters);
                }
            });

            // Function to show full content in modal
            function showFullContent(title, content, unit, dept, author, date) {
                const modal = document.getElementById('contentModal');
                const modalTitle = document.getElementById('modalTitle');
                const modalMeta = document.getElementById('modalMeta');
                const modalBody = document.getElementById('modalBody');
                
                // 使用增强的标题清理函数
                modalTitle.textContent = cleanTitle(title);
                
                // Set metadata
                modalMeta.innerHTML = `
                    <div class="modal-meta-item">发布单位: ${unit}</div>
                    <div class="modal-meta-item">发布部门: ${dept}</div>
                    <div class="modal-meta-item">发布人: ${author}</div>
                    <div class="modal-meta-item">发布日期: ${date}</div>
                `;
                
                // 使用增强的内容清理函数
                const originalContent = content;
                const cleanedContent = window.cleanHtml(content, true);
                
                // 检测乱码并添加清理状态提示
                let cleaningInfo = '';
                if (window.detectGarbledText && window.detectGarbledText(originalContent)) {
                    const garbledRatio = window.getGarbledRatio ? window.getGarbledRatio(originalContent) : 0;
                    cleaningInfo = `<div class="cleaning-info" style="background: #e3f2fd; border: 1px solid #2196f3; padding: 8px; border-radius: 4px; margin-bottom: 15px; font-size: 13px; color: #1976d2;">
                        <i class="fas fa-broom"></i> 已自动清理文本乱码 (检测到 ${(garbledRatio * 100).toFixed(1)}% 的异常字符)
                    </div>`;
                    console.log(`已清理内容乱码，原始长度: ${originalContent.length}, 清理后长度: ${cleanedContent.length}`);
                }
                
                modalBody.innerHTML = cleaningInfo + `<div class="standardized-content">${cleanedContent}</div>`;
                
                // Show modal
                modal.style.display = 'block';
                
                // Prevent scroll on body
                document.body.style.overflow = 'hidden';
            }
            
            // Close modal when clicking close button
            document.getElementById('closeModal').addEventListener('click', function() {
                document.getElementById('contentModal').style.display = 'none';
                document.body.style.overflow = 'auto';
            });
            
            // Close modal when clicking outside of it
            document.getElementById('contentModal').addEventListener('click', function(event) {
                if (event.target === this) {
                    this.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });

            // 初始化过滤器选项
            async function initializeFilters() {
                try {
                    const response = await fetch('/api/filter_options');
                    if (response.ok) {
                        const options = await response.json();
                        
                        // 填充单位过滤器
                        const unitFilter = document.getElementById('unitFilter');
                        if (unitFilter && options.units) {
                            options.units.forEach(unit => {
                                const option = document.createElement('option');
                                option.value = unit;
                                option.textContent = unit;
                                unitFilter.appendChild(option);
                            });
                        }
                        
                        // 填充部门过滤器
                        const deptFilter = document.getElementById('deptFilter');
                        if (deptFilter && options.departments) {
                            options.departments.forEach(dept => {
                                const option = document.createElement('option');
                                option.value = dept;
                                option.textContent = dept;
                                deptFilter.appendChild(option);
                            });
                        }
                    }
                } catch (error) {
                    console.error('Failed to load filter options:', error);
                }
            }

            // 初始化
            initializeFilters();
            window.fetchData(currentPage, keyword);
        });
    </script>
    
    <!-- 增强搜索功能脚本 -->
    <script src="{{ url_for('static', filename='js/enhanced-search.js') }}"></script>
    
    <!-- 收藏夹管理功能脚本 -->
    <script src="{{ url_for('static', filename='js/favorites-manager.js') }}"></script>
    
    <!-- 缓存管理功能脚本 -->
    <script src="{{ url_for('static', filename='js/cache-manager.js') }}"></script>
</body>
</html>