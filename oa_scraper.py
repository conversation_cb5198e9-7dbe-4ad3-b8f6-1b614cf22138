#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA数据爬虫程序
抓取汕头大学OA系统数据
使用发现的API端点：http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
目标：抓取1万条记录
"""

import requests
import csv
import time
import json
import logging
import re
from datetime import datetime
from urllib.parse import quote
try:
    from ..utils.config import Config
except ImportError:
    # 如果相对导入失败，使用本地配置
    from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OAScraper:
    def __init__(self):
        # 从配置文件加载配置
        oa_config = Config.get_oa_config()
        
        self.api_url = oa_config['api_url']
        self.doc_num_url = oa_config['doc_num_url']
        self.token = oa_config['token']
        self.timeout = oa_config['timeout']
        self.records_per_batch = oa_config['records_per_batch']
        self.delay = oa_config['delay']
        
        self.token_encoded = quote(self.token)
        self.referrer = f"{oa_config['base_url']}?TokenOa={self.token_encoded}&PageContainsRecord=10&CurrentPageNo=1"

        # 设置requests session
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6,ja;q=0.5',
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Pragma': 'no-cache',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.referrer,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        self.records = []
        self.total_target = 3000  # 目标抓取3000条记录
        self.total_docs = None  # 总文档数
        
    def clean_garbled_text(self, text):
        """清理文本中的乱码字符"""
        if not text:
            return ""
        
        # 确保文本是字符串类型
        if not isinstance(text, str):
            text = str(text)
        
        try:
            # 使用文本清理工具进行乱码过滤
            from text_cleaner import clean_garbled_text
            cleaned_text = clean_garbled_text(text, aggressive=False)
            
            # 记录清理统计
            if len(cleaned_text) != len(text):
                logging.info(f"文本清理：原始长度 {len(text)}，清理后长度 {len(cleaned_text)}")
            
            return cleaned_text
            
        except ImportError:
            # 如果导入失败，使用简单的清理方法
            logging.warning("文本清理工具不可用，使用简单清理方法")
            return self._simple_text_clean(text)
        except Exception as e:
            # 如果清理过程出错，记录错误并返回简单清理的结果
            logging.error(f"文本清理过程中发生错误: {e}")
            return self._simple_text_clean(text)
    
    def _simple_text_clean(self, text):
        """简单的文本清理方法（备用）"""
        import re
        
        # 移除控制字符
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # 移除零宽字符
        text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)
        
        # 修复常见编码错误
        replacements = {
            'â€œ': '"',
            'â€': '"', 
            'â€™': "'",
            'â€˜': "'",
            'ï¿½': '',
            '�': ''
        }
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        # 规范化空白字符
        text = re.sub(r'\s+', ' ', text)
        return text.strip() if text.strip() else "无内容"
        
    def get_doc_num(self, keyword=''):
        """获取文档总数"""
        try:
            # 准备POST数据，使用URL编码的token
            data = {
                'token': self.token_encoded,
                'subcompany_id': '0',
                'keyword': keyword
            }

            logging.info(f"正在获取文档总数...")
            logging.info(f"请求URL: {self.doc_num_url}")
            logging.info(f"请求数据: {data}")

            response = self.session.post(self.doc_num_url, data=data, timeout=self.timeout)
            response.raise_for_status()

            logging.info(f"GetDocNum响应状态码: {response.status_code}")
            logging.info(f"响应Content-Type: {response.headers.get('Content-Type', 'unknown')}")
            logging.info(f"响应内容长度: {len(response.text)}")

            # 检查是否是HTML响应
            content_type = response.headers.get('Content-Type', '').lower()
            if 'text/html' in content_type or response.text.strip().startswith('<'):
                logging.warning(f"服务器返回HTML错误页面，可能是数据库连接问题")
                logging.info(f"响应内容前200字符: {response.text[:200]}")
                # 尝试从HTML中提取错误信息
                import re
                if 'Oracle' in response.text or '数据库' in response.text or '连接' in response.text:
                    logging.error("服务器数据库连接异常，无法获取文档总数")
                    return None
                # 设置一个默认值继续尝试
                self.total_docs = 3000
                logging.info(f"使用默认文档总数: {self.total_docs}")
                return self.total_docs

            # 处理空响应
            if not response.text.strip():
                logging.warning(f"服务器返回空响应，使用默认文档总数")
                self.total_docs = 3000
                logging.info(f"使用默认文档总数: {self.total_docs}")
                return self.total_docs

            # 尝试解析响应
            try:
                result = response.json()
                if isinstance(result, dict) and 'total' in result:
                    self.total_docs = int(result['total'])
                elif response.text.isdigit():
                    self.total_docs = int(response.text)
                else:
                    logging.warning(f"无法解析文档总数: {response.text}")
                    self.total_docs = 3000

                logging.info(f"文档总数: {self.total_docs}")
                return self.total_docs

            except (json.JSONDecodeError, ValueError) as e:
                logging.error(f"解析文档总数失败: {e}")
                # 使用默认值
                self.total_docs = 3000
                logging.info(f"使用默认文档总数: {self.total_docs}")
                return self.total_docs

        except requests.exceptions.RequestException as e:
            logging.error(f"获取文档总数失败：{e}")
            return None
        except Exception as e:
            logging.error(f"处理文档总数请求时发生错误：{e}")
            return None

    def get_batch_data(self, row_start, row_end, keyword=''):
        """获取指定范围的数据"""
        try:
            # 准备POST数据，使用URL编码的token
            data = {
                'token': self.token_encoded,
                'subcompany_id': '0',
                'keyword': keyword,
                'row_start': str(row_start),
                'row_end': str(row_end)
            }

            logging.info(f"正在获取第{row_start}-{row_end}条记录...")
            logging.info(f"请求URL: {self.api_url}")
            logging.info(f"请求数据: {data}")

            response = self.session.post(self.api_url, data=data, timeout=self.timeout)
            response.raise_for_status()

            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应Content-Type: {response.headers.get('Content-Type', 'unknown')}")
            logging.info(f"响应内容长度: {len(response.text)}")

            # 检查是否是HTML响应
            content_type = response.headers.get('Content-Type', '').lower()
            response_text = response.text.strip()
            
            # 更精确的HTML检测：只有当整个响应是HTML页面时才认为是错误
            if ('text/html' in content_type and 
                (response_text.startswith('<!DOCTYPE') or 
                 response_text.startswith('<html') or
                 response_text.startswith('<head') or
                 response_text.startswith('<body'))):
                logging.warning(f"服务器返回HTML错误页面，可能是数据库连接问题")
                logging.info(f"响应内容前200字符: {response_text[:200]}")
                # 返回错误信息以便后续处理
                return {
                    'error': 'database_connection_error',
                    'message': '服务器数据库连接异常',
                    'raw_response': response_text,
                    'content_type': content_type
                }
            
            # 如果响应以[{"开头，很可能是JSON数据，即使包含HTML内容
            if response_text.startswith('[{') or response_text.startswith('{'):
                logging.info(f"检测到JSON格式响应，尝试解析")
                # 继续后续的JSON解析流程

            # 处理空响应
            if not response.text.strip():
                logging.warning(f"服务器返回空响应")
                return {
                    'error': 'empty_response',
                    'message': '服务器返回空响应',
                    'raw_response': ''
                }

            # 尝试解析JSON响应
            try:
                json_data = response.json()
                logging.info(f"成功解析JSON响应")
                return json_data
            except json.JSONDecodeError as e:
                logging.warning(f"响应不是有效的JSON格式: {e}")
                logging.info(f"响应内容: {response.text[:500]}")
                return {'raw_response': response.text}

        except requests.exceptions.RequestException as e:
            logging.error(f"获取第{row_start}-{row_end}条记录失败：{e}")
            return None
        except Exception as e:
            logging.error(f"处理第{row_start}-{row_end}条记录时发生错误：{e}")
            return None
    
    def parse_api_response(self, api_response, batch_start, batch_end):
        """解析API响应数据"""
        try:
            batch_records = []

            if api_response is None:
                return batch_records

            # 处理错误响应
            if 'error' in api_response:
                error_type = api_response.get('error', 'unknown_error')
                error_message = api_response.get('message', '未知错误')
                logging.warning(f"第{batch_start}-{batch_end}条记录返回错误: {error_type} - {error_message}")
                
                record = {
                    'batch_start': batch_start,
                    'batch_end': batch_end,
                    'error_type': error_type,
                    'error_message': error_message,
                    'content': api_response.get('raw_response', '')[:500] + "..." if len(api_response.get('raw_response', '')) > 500 else api_response.get('raw_response', ''),
                    'timestamp': datetime.now().isoformat(),
                    'data_type': 'error_response'
                }
                batch_records.append(record)
                return batch_records

            # 如果是原始响应文本
            if 'raw_response' in api_response:
                logging.warning(f"第{batch_start}-{batch_end}条记录返回非JSON响应")
                record = {
                    'batch_start': batch_start,
                    'batch_end': batch_end,
                    'content': api_response['raw_response'][:500] + "..." if len(api_response['raw_response']) > 500 else api_response['raw_response'],
                    'timestamp': datetime.now().isoformat(),
                    'data_type': 'raw_response'
                }
                batch_records.append(record)
                return batch_records

            # 处理JSON响应 - 检查是否直接是数组
            if isinstance(api_response, list):
                logging.info(f"响应是数组格式，包含{len(api_response)}条记录")
                for i, item in enumerate(api_response):
                    if isinstance(item, dict):
                        # 提取文档的关键信息
                        doc_content = item.get('DOCCONTENT', '')
                        doc_title = item.get('DOCTITLE', '')
                        doc_id = item.get('DOCID', '')
                        doc_date = item.get('DOCDATE', '')
                        doc_unit = item.get('DOCUNIT', '')

                        # 清理乱码文本
                        cleaned_title = self.clean_garbled_text(doc_title)
                        cleaned_content = self.clean_garbled_text(doc_content)
                        cleaned_unit = self.clean_garbled_text(doc_unit)

                        record = {
                            'batch_start': batch_start,
                            'batch_end': batch_end,
                            'item_index': i + 1,
                            'doc_id': doc_id,
                            'doc_title': cleaned_title,
                            'doc_content': cleaned_content[:1000] + "..." if len(cleaned_content) > 1000 else cleaned_content,  # 截取前1000字符
                            'doc_date': doc_date,
                            'doc_unit': cleaned_unit,
                            'full_content': cleaned_content,  # 保存完整内容
                            'raw_data': item,
                            'timestamp': datetime.now().isoformat(),
                            'data_type': 'document',
                            'cleaning_applied': True  # 标记已应用文本清理
                        }
                        batch_records.append(record)
                    else:
                        # 如果不是字典，直接保存
                        record = {
                            'batch_start': batch_start,
                            'batch_end': batch_end,
                            'item_index': i + 1,
                            'content': str(item),
                            'raw_data': item,
                            'timestamp': datetime.now().isoformat(),
                            'data_type': 'simple_item'
                        }
                        batch_records.append(record)

                logging.info(f"第{batch_start}-{batch_end}条记录解析完成，获得{len(batch_records)}条记录")
                return batch_records

            # 处理字典格式的JSON响应
            if isinstance(api_response, dict):
                # 检查常见的数据字段
                data_fields = ['data', 'list', 'items', 'records', 'docs', 'documents']
                data_found = False

                for field in data_fields:
                    if field in api_response and api_response[field]:
                        data_list = api_response[field]
                        if isinstance(data_list, list):
                            logging.info(f"在字段'{field}'中找到{len(data_list)}条记录")
                            for i, item in enumerate(data_list):
                                record = {
                                    'batch_start': batch_start,
                                    'batch_end': batch_end,
                                    'item_index': i + 1,
                                    'content': str(item),
                                    'raw_data': item,
                                    'timestamp': datetime.now().isoformat(),
                                    'data_type': 'structured_data'
                                }
                                batch_records.append(record)
                            data_found = True
                            break

                # 如果没有找到标准数据字段，保存整个响应
                if not data_found:
                    logging.info(f"第{batch_start}-{batch_end}条记录：未找到标准数据字段，保存完整响应")
                    record = {
                        'batch_start': batch_start,
                        'batch_end': batch_end,
                        'content': json.dumps(api_response, ensure_ascii=False, indent=2),
                        'raw_data': api_response,
                        'timestamp': datetime.now().isoformat(),
                        'data_type': 'full_response'
                    }
                    batch_records.append(record)

            logging.info(f"第{batch_start}-{batch_end}条记录解析完成，获得{len(batch_records)}条记录")
            return batch_records

        except Exception as e:
            logging.error(f"解析第{batch_start}-{batch_end}条记录失败：{e}")
            return []

    def realtime_request(self, page=1, limit=20, keyword=''):
        """实时API请求，根据查询参数获取数据"""
        try:
            logging.info(f"实时API请求，参数: page={page}, limit={limit}, keyword='{keyword}'")
            
            row_start = (page - 1) * limit
            row_end = row_start + limit -1

            # 调用核心方法获取和解析数据
            api_response = self.get_batch_data(row_start, row_end, keyword)
            if api_response is None:
                return {"error": "Failed to fetch data from the source API."}

            # 解析数据
            records = self.parse_api_response(api_response, row_start, row_end)
            
            # 获取总数用于分页
            total_docs = self.get_doc_num(keyword=keyword)
            if total_docs is None:
                total_docs = -1 # 表示获取失败

            return {
                'total': total_docs,
                'page': page,
                'limit': limit,
                'data': records
            }

        except Exception as e:
            logging.error(f"实时请求处理失败: {e}")
            return {"error": str(e)}

    def enhanced_search(self, page=1, limit=20, keyword='', unit='', dept='', start_date='', end_date='', author='', min_length='', max_length=''):
        """增强搜索功能，支持多维度筛选"""
        try:
            logging.info(f"增强搜索请求，参数: page={page}, limit={limit}, keyword='{keyword}', unit='{unit}', dept='{dept}', start_date='{start_date}', end_date='{end_date}', author='{author}'")
            
            # 使用现有的实时请求方法作为基础
            result = self.realtime_request(page=page, limit=limit, keyword=keyword)
            
            if 'error' in result:
                return result
            
            # 如果有筛选条件，对结果进行过滤
            filtered_data = []
            for item in result['data']:
                if self._matches_filters(item, unit, dept, start_date, end_date, author, min_length, max_length):
                    filtered_data.append(item)
            
            # 更新结果
            result['data'] = filtered_data
            result['filtered_total'] = len(filtered_data)
            result['original_total'] = result['total']
            
            logging.info(f"搜索结果: 原始{result['original_total']}条，筛选后{result['filtered_total']}条")
            return result
            
        except Exception as e:
            logging.error(f"增强搜索失败: {e}")
            return {"error": str(e)}
    
    def _matches_filters(self, item, unit='', dept='', start_date='', end_date='', author='', min_length='', max_length=''):
        """检查项目是否匹配筛选条件"""
        try:
            raw_data = item.get('raw_data', {})
            
            # 单位筛选
            if unit:
                item_unit = self.clean_garbled_text(raw_data.get('SUBCOMPANYNAME', raw_data.get('DOCUNIT', ''))).lower()
                if unit.lower() not in item_unit:
                    return False
            
            # 部门筛选
            if dept:
                item_dept = self.clean_garbled_text(raw_data.get('DEPARTMENTNAME', '')).lower()
                if dept.lower() not in item_dept:
                    return False
            
            # 日期筛选
            if start_date or end_date:
                item_date = raw_data.get('DOCVALIDDATE', raw_data.get('DOCDATE', ''))
                if item_date and item_date != '未知':
                    try:
                        from datetime import datetime
                        item_dt = datetime.strptime(item_date, '%Y-%m-%d').date()
                        
                        if start_date:
                            start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
                            if item_dt < start_dt:
                                return False
                        
                        if end_date:
                            end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
                            if item_dt > end_dt:
                                return False
                    except ValueError:
                        # 如果日期解析失败，跳过该记录
                        return False
            
            # 发布人筛选
            if author:
                item_author = self.clean_garbled_text(raw_data.get('LASTNAME', '')).lower()
                if author.lower() not in item_author:
                    return False
            
            # 内容长度筛选
            content = self.clean_garbled_text(raw_data.get('DOCCONTENT', ''))
            content_length = len(content) if content else 0
            
            if min_length:
                try:
                    min_len = int(min_length)
                    if content_length < min_len:
                        return False
                except ValueError:
                    pass
            
            if max_length:
                try:
                    max_len = int(max_length)
                    if content_length > max_len:
                        return False
                except ValueError:
                    pass
            
            return True
            
        except Exception as e:
            logging.error(f"筛选条件检查失败: {e}")
            return False

    def get_search_suggestions(self, query, limit=10):
        """获取搜索建议"""
        try:
            # 获取一些数据来提取建议
            result = self.realtime_request(page=1, limit=50, keyword=query)
            
            if 'error' in result:
                return []
            
            suggestions = set()
            for item in result['data']:
                raw_data = item.get('raw_data', {})
                
                # 从标题中提取建议
                title = raw_data.get('DOCTITLE', '')
                cleaned_title = self.clean_garbled_text(title)
                if cleaned_title and query.lower() in cleaned_title.lower():
                    suggestions.add(cleaned_title)
                
                # 从内容中提取包含查询词的短语
                content = raw_data.get('DOCCONTENT', '')
                cleaned_content = self.clean_garbled_text(content)
                if cleaned_content:
                    # 简单的短语提取
                    words = content.split()
                    for i, word in enumerate(words):
                        if query.lower() in word.lower() and len(word) > 2:
                            # 提取前后词组成短语
                            phrase_words = words[max(0, i-2):i+3]
                            phrase = ' '.join(phrase_words)
                            if len(phrase) <= 50:  # 限制短语长度
                                suggestions.add(phrase)
            
            return list(suggestions)[:limit]
            
        except Exception as e:
            logging.error(f"获取搜索建议失败: {e}")
            return []

    def get_popular_keywords(self, limit=20):
        """获取热门关键词"""
        try:
            # 获取最近的数据来分析关键词
            result = self.realtime_request(page=1, limit=100)
            
            if 'error' in result:
                return []
            
            keywords = {}
            for item in result['data']:
                raw_data = item.get('raw_data', {})
                
                # 从标题和内容中提取关键词
                title = self.clean_garbled_text(raw_data.get('DOCTITLE', ''))
                content = self.clean_garbled_text(raw_data.get('DOCCONTENT', ''))
                text = (title + ' ' + content).lower()
                
                # 简单的关键词提取
                words = text.split()
                for word in words:
                    # 过滤掉太短的词和常见词
                    if len(word) > 2 and word not in ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '它', '他', '她', '们', '为', '以', '从', '而', '与', '或', '但', '如果', '因为', '所以', '可以', '能够', '应该', '需要', '必须', '可能', '已经', '正在', '将要', '进行', '工作', '问题', '事情', '情况', '条件', '要求', '规定', '通知', '公告', '文件', '报告', '关于', '根据', '按照', '通过', '经过', '对于', '由于', '除了', '包括', '以及', '同时', '另外', '此外', '而且', '但是', '然而', '因此', '所以', '于是', '这样', '那样', '如何', '什么', '为什么', '哪里', '什么时候', '谁', '多少', '几个', '哪些', '怎样', '怎么', '怎么样', '是否', '能不能', '可不可以', '应该不应该', '必须不必须', '需要不需要', '要不要', '好不好', '对不对', '行不行', '可以不可以', '能不能够']:
                        keywords[word] = keywords.get(word, 0) + 1
            
            # 按频率排序
            sorted_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)
            return [word for word, count in sorted_keywords[:limit]]
            
        except Exception as e:
            logging.error(f"获取热门关键词失败: {e}")
            return []

    def get_filter_options(self):
        """获取过滤器选项"""
        try:
            # 获取一些数据来提取选项
            result = self.realtime_request(page=1, limit=200)
            
            if 'error' in result:
                return {'units': [], 'departments': [], 'authors': []}
            
            units = set()
            departments = set()
            authors = set()
            
            for item in result['data']:
                raw_data = item.get('raw_data', {})
                
                # 提取单位
                unit = raw_data.get('SUBCOMPANYNAME', raw_data.get('DOCUNIT', ''))
                cleaned_unit = self.clean_garbled_text(unit)
                if cleaned_unit and cleaned_unit != '未知' and cleaned_unit != '无内容':
                    units.add(cleaned_unit)
                
                # 提取部门
                dept = raw_data.get('DEPARTMENTNAME', '')
                cleaned_dept = self.clean_garbled_text(dept)
                if cleaned_dept and cleaned_dept != '未知' and cleaned_dept != '无内容':
                    departments.add(cleaned_dept)
                
                # 提取作者
                author = raw_data.get('LASTNAME', '')
                cleaned_author = self.clean_garbled_text(author)
                if cleaned_author and cleaned_author != '未知' and cleaned_author != '无内容':
                    authors.add(cleaned_author)
            
            return {
                'units': sorted(list(units)),
                'departments': sorted(list(departments)),
                'authors': sorted(list(authors))
            }
            
        except Exception as e:
            logging.error(f"获取过滤器选项失败: {e}")
            return {'units': [], 'departments': [], 'authors': []}


