# OA信息Web应用 - Render部署指南

## 项目结构
```
deploy/
├── web_app.py           # Flask应用主文件
├── oa_scraper.py        # OA数据爬虫
├── cache_manager.py     # 缓存管理器
├── favorites_manager.py # 收藏夹管理器
├── config.py           # 配置文件
├── requirements.txt    # Python依赖
├── render.yaml         # Render配置文件
├── templates/          # HTML模板
└── static/            # 静态文件
```

## 部署步骤

### 1. 上传到GitHub
1. 将整个`deploy`文件夹的内容上传到你的GitHub仓库
2. 确保所有文件都已提交

### 2. 在Render上创建服务
1. 访问 [render.com](https://render.com) 并注册账号
2. 点击 "New +" → "Web Service"
3. 选择 "Build and deploy from a Git repository"
4. 连接你的GitHub仓库
5. 选择包含这些文件的仓库

### 3. 配置服务
- Render会自动检测为Python项目
- 会自动使用`render.yaml`配置文件
- 自动安装`requirements.txt`中的依赖

### 4. 部署完成
- 点击"Create Web Service"
- Render会自动构建和部署
- 部署成功后提供一个URL访问应用

## 配置说明

### 环境变量
- `FLASK_ENV=production` - 生产环境模式
- `PORT=10000` - 监听端口
- `PYTHON_VERSION=3.9.0` - Python版本

### 持久化存储
- 配置了1GB的磁盘空间用于缓存
- 缓存路径：`/opt/render/project/cache`

### 启动命令
- 使用gunicorn作为WSGI服务器
- 命令：`gunicorn web_app:app`

## 注意事项

1. **免费套餐限制**：
   - 15分钟无访问会休眠
   - 每月有带宽限制
   - 内存限制512MB

2. **首次部署**：
   - 可能需要几分钟时间
   - 查看构建日志确认无错误

3. **访问问题**：
   - 部署后等待1-2分钟再访问
   - 检查Render仪表板的服务状态

4. **缓存数据**：
   - 缓存数据会持久化保存
   - 重新部署不会丢失缓存

## 故障排除

如果部署失败：
1. 检查Render的构建日志
2. 确认所有依赖都在requirements.txt中
3. 检查Python语法错误
4. 查看Render的服务状态页面

## 访问应用

部署成功后，通过Render提供的URL访问你的OA信息应用。# STU-OA
