// OA数据抓取与可视化系统 - 主应用程序
class OAApp {
    constructor() {
        this.currentPage = 1;
        this.limit = 20;
        this.keyword = '';
        this.isLoading = false;
        this.hasMore = true;
        this.currentFilters = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initializeData();
    }
    
    bindEvents() {
        // 如果增强搜索功能存在，让增强搜索处理搜索事件
        if (window.enhancedSearch) {
            console.log('增强搜索功能已启用');
        } else {
            // 基础搜索事件绑定
            const searchButton = document.getElementById('searchBtn');
            const searchInput = document.getElementById('keywordInput');
            
            if (searchButton) {
                searchButton.addEventListener('click', () => this.performSearch());
            }
            
            if (searchInput) {
                searchInput.addEventListener('keyup', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });
            }
        }
        
        // 滚动加载更多
        window.addEventListener('scroll', () => {
            if (this.isLoading || !this.hasMore) return;
            
            if (window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 100) {
                this.loadMore();
            }
        });
        
        // 其他功能按钮事件
        this.bindControlPanelEvents();
    }
    
    bindControlPanelEvents() {
        // 数据刷新
        const refreshBtn = document.getElementById('refreshData');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }
        
        // 应用筛选
        const applyFilterBtn = document.getElementById('applyFilter');
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', () => this.applyFilters());
        }
        
        // 清除筛选
        const clearFilterBtn = document.getElementById('clearFilter');
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', () => this.clearFilters());
        }
        
        // 导出功能
        const exportJSONBtn = document.getElementById('exportJSON');
        const exportCSVBtn = document.getElementById('exportCSV');
        
        if (exportJSONBtn) {
            exportJSONBtn.addEventListener('click', () => this.exportData('json'));
        }
        
        if (exportCSVBtn) {
            exportCSVBtn.addEventListener('click', () => this.exportData('csv'));
        }
        
        // 数据发现
        const discoverBtn = document.getElementById('discoverData');
        if (discoverBtn) {
            discoverBtn.addEventListener('click', () => this.discoverDataFiles());
        }
        
        // 数据导入
        const importBtn = document.getElementById('importData');
        if (importBtn) {
            importBtn.addEventListener('click', () => this.importData());
        }
        
        // 实时API
        const fetchLatestBtn = document.getElementById('fetchLatest');
        const realtimeAPIBtn = document.getElementById('realtimeAPI');
        
        if (fetchLatestBtn) {
            fetchLatestBtn.addEventListener('click', () => this.fetchLatestData());
        }
        
        if (realtimeAPIBtn) {
            realtimeAPIBtn.addEventListener('click', () => this.showAPIModal());
        }
    }
    
    async performSearch() {
        const searchInput = document.getElementById('keywordInput');
        if (searchInput) {
            this.keyword = searchInput.value.trim();
        }
        
        this.currentPage = 1;
        this.hasMore = true;
        await this.fetchData();
    }
    
    async fetchData(page = this.currentPage, keyword = this.keyword, reset = false) {
        if (this.isLoading || !this.hasMore) return;
        
        this.isLoading = true;
        this.showLoading(true);
        
        try {
            const url = `/api/get_oa_data?page=${page}&limit=${this.limit}&keyword=${encodeURIComponent(keyword)}`;
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP 错误! 状态: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (reset) {
                this.clearDataContainer();
            }
            
            this.renderData(result.data);
            
            // 检查是否还有更多数据
            const totalFetched = ((page - 1) * this.limit) + result.data.length;
            this.hasMore = result.total > 0 && totalFetched < result.total;
            
            if (!this.hasMore) {
                this.showNoMoreData();
            }
            
            this.updateTotalRecords(result.total);
            
        } catch (error) {
            console.error('获取数据失败:', error);
            this.showError(`获取数据失败: ${error.message}`);
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }
    
    async loadMore() {
        this.currentPage++;
        await this.fetchData(this.currentPage, this.keyword);
    }
    
    renderData(items) {
        const container = document.getElementById('dataTableBody') || document.getElementById('dataContainer');
        if (!container) return;
        
        if (!items || items.length === 0) {
            if (this.currentPage === 1) {
                container.innerHTML = '<tr><td colspan="6" class="text-center">未找到相关数据</td></tr>';
            }
            return;
        }
        
        items.forEach(item => {
            const doc = item.raw_data || item;
            const row = this.createDataRow(doc);
            container.appendChild(row);
        });
    }
    
    createDataRow(doc) {
        const row = document.createElement('tr');
        
        // 提取标题
        let title = doc.DOCTITLE || '无标题';
        if (!title && doc.DOCCONTENT) {
            const match = doc.DOCCONTENT.match(/关于.*?的通知|.*?通知|.*?公告|.*?文件/);
            if (match) {
                title = match[0];
            } else {
                title = doc.DOCCONTENT.substring(0, 50) + '...';
            }
        }
        
        // 提取元数据
        const unit = doc.SUBCOMPANYNAME || doc.DOCUNIT || '未知';
        const date = doc.DOCVALIDDATE || doc.DOCDATE || '未知';
        const author = doc.LASTNAME || '未知';
        const id = doc.DOCID || doc.ID || '未知';
        
        row.innerHTML = `
            <td>${id}</td>
            <td>
                <a href="#" class="text-primary text-decoration-none" onclick="oaApp.showDetails('${id}')">
                    ${title}
                </a>
            </td>
            <td>${unit}</td>
            <td>${date}</td>
            <td>${author}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="oaApp.showDetails('${id}')">
                    <i class="fas fa-eye"></i> 查看
                </button>
            </td>
        `;
        
        return row;
    }
    
    cleanText(text) {
        if (!text) return '';
        
        // 确保文本是字符串类型
        if (typeof text !== 'string') {
            text = String(text);
        }
        
        // 直接返回原始文本，不做任何清理
        return text;
    }
    
    async showDetails(id) {
        try {
            const response = await fetch(`/api/get_oa_data?keyword=${id}&limit=1`);
            const result = await response.json();
            
            if (result.data && result.data.length > 0) {
                const doc = result.data[0].raw_data;
                this.showDetailsModal(doc);
            }
        } catch (error) {
            console.error('获取详情失败:', error);
            this.showError('获取详情失败');
        }
    }
    
    showDetailsModal(doc) {
        const modal = document.getElementById('detailsModal') || document.getElementById('contentModal');
        if (!modal) return;
        
        const title = doc.DOCTITLE || '无标题';
        const content = doc.DOCCONTENT || '';
        const unit = doc.SUBCOMPANYNAME || doc.DOCUNIT || '未知';
        const date = doc.DOCVALIDDATE || doc.DOCDATE || '未知';
        const author = doc.LASTNAME || '未知';
        
        // 填充模态框内容
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody') || document.getElementById('detailsContent');
        const modalMeta = document.getElementById('modalMeta');
        
        if (modalTitle) modalTitle.textContent = title;
        if (modalBody) modalBody.innerHTML = this.cleanContent(content);
        if (modalMeta) {
            modalMeta.innerHTML = `
                <div class="modal-meta-item">发布单位: ${unit}</div>
                <div class="modal-meta-item">发布人: ${author}</div>
                <div class="modal-meta-item">发布日期: ${date}</div>
            `;
        }
        
        // 显示模态框
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
    
    cleanContent(content) {
        if (!content) return '';
        
        // 直接返回原始内容，不做任何清理
        return `<div class="standardized-content">${content}</div>`;
    }
    
    async refreshData() {
        this.currentPage = 1;
        this.hasMore = true;
        await this.fetchData();
    }
    
    async applyFilters() {
        // 获取筛选条件
        const filters = {
            unit: document.getElementById('unitFilter')?.value || '',
            startDate: document.getElementById('dateStart')?.value || '',
            endDate: document.getElementById('dateEnd')?.value || '',
            keyword: document.getElementById('keywordFilter')?.value || ''
        };
        
        this.currentFilters = filters;
        this.currentPage = 1;
        this.hasMore = true;
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: this.currentPage,
            limit: this.limit,
            keyword: filters.keyword,
            unit: filters.unit,
            startDate: filters.startDate,
            endDate: filters.endDate
        });
        
        try {
            const response = await fetch(`/api/get_oa_data?${params}`);
            const result = await response.json();
            
            this.clearDataContainer();
            this.renderData(result.data);
            
        } catch (error) {
            console.error('应用筛选失败:', error);
            this.showError('应用筛选失败');
        }
    }
    
    clearFilters() {
        // 清除所有筛选条件
        const filterInputs = ['unitFilter', 'dateStart', 'dateEnd', 'keywordFilter'];
        filterInputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });
        
        this.currentFilters = {};
        this.refreshData();
    }
    
    async exportData(format) {
        try {
            const params = new URLSearchParams({
                keyword: this.keyword,
                limit: 1000
            });
            
            const response = await fetch(`/api/get_oa_data?${params}`);
            const result = await response.json();
            
            if (result.data && result.data.length > 0) {
                this.downloadFile(result.data, format);
            } else {
                this.showError('没有数据可导出');
            }
        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出失败');
        }
    }
    
    downloadFile(data, format) {
        let content, filename, type;
        
        if (format === 'json') {
            content = JSON.stringify(data, null, 2);
            filename = `oa_data_${new Date().toISOString().split('T')[0]}.json`;
            type = 'application/json';
        } else if (format === 'csv') {
            content = this.convertToCSV(data);
            filename = `oa_data_${new Date().toISOString().split('T')[0]}.csv`;
            type = 'text/csv';
        }
        
        const blob = new Blob([content], { type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    }
    
    convertToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = ['ID', '标题', '发布单位', '发布日期', '发布人', '内容'];
        const rows = data.map(item => {
            const doc = item.raw_data || item;
            return [
                doc.DOCID || doc.ID || '',
                doc.DOCTITLE || '',
                doc.SUBCOMPANYNAME || doc.DOCUNIT || '',
                doc.DOCVALIDDATE || doc.DOCDATE || '',
                doc.LASTNAME || '',
                (doc.DOCCONTENT || '').substring(0, 200)
            ];
        });
        
        return [headers, ...rows].map(row => 
            row.map(cell => `"${cell}"`).join(',')
        ).join('\n');
    }
    
    async discoverDataFiles() {
        // 显示发现数据文件的模态框
        const modal = document.getElementById('discoverModal');
        if (modal) {
            modal.style.display = 'block';
            
            // 模拟搜索过程
            const results = document.getElementById('discoverResults');
            results.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">搜索中...</span>
                    </div>
                    <p class="mt-2">正在搜索数据文件...</p>
                </div>
            `;
            
            // 模拟搜索结果
            setTimeout(() => {
                results.innerHTML = `
                    <div class="list-group">
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">oa_data_2024.json</h6>
                                    <small class="text-muted">大小: 2.5MB | 修改时间: 2024-01-15</small>
                                </div>
                                <input type="checkbox" class="form-check-input">
                            </div>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">oa_archive_2023.json</h6>
                                    <small class="text-muted">大小: 5.8MB | 修改时间: 2023-12-20</small>
                                </div>
                                <input type="checkbox" class="form-check-input">
                            </div>
                        </div>
                    </div>
                `;
            }, 2000);
        }
    }
    
    async importData() {
        const modal = document.getElementById('importModal');
        if (modal) {
            modal.style.display = 'block';
            
            // 模拟导入过程
            const progressBar = document.getElementById('importProgressBar');
            const status = document.getElementById('importStatus');
            const log = document.getElementById('importLog');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                status.textContent = `正在导入... ${progress}%`;
                
                if (progress >= 100) {
                    clearInterval(interval);
                    status.textContent = '导入完成';
                    log.innerHTML += '<div>✓ 数据导入成功</div>';
                }
            }, 500);
        }
    }
    
    async fetchLatestData() {
        const limit = document.getElementById('latestLimit')?.value || 50;
        try {
            const response = await fetch(`/api/get_oa_data?limit=${limit}`);
            const result = await response.json();
            
            this.clearDataContainer();
            this.renderData(result.data);
            
        } catch (error) {
            console.error('获取最新数据失败:', error);
            this.showError('获取最新数据失败');
        }
    }
    
    showAPIModal() {
        const modal = document.getElementById('apiModal');
        if (modal) {
            modal.style.display = 'block';
        }
    }
    
    clearDataContainer() {
        const container = document.getElementById('dataTableBody') || document.getElementById('dataContainer');
        if (container) {
            container.innerHTML = '';
        }
    }
    
    showLoading(show) {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }
    
    showNoMoreData() {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.textContent = '没有更多数据了';
            loader.style.display = 'block';
        }
    }
    
    updateTotalRecords(total) {
        const totalElement = document.getElementById('totalRecords');
        if (totalElement) {
            totalElement.textContent = total;
        }
    }
    
    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            
            setTimeout(() => {
                errorElement.style.display = 'none';
            }, 5000);
        }
    }
    
    initializeData() {
        // 初始化时加载一些数据
        this.fetchData();
    }
}

// 全局函数，供HTML中的onclick事件调用
function showDetails(id) {
    if (window.oaApp) {
        window.oaApp.showDetails(id);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.oaApp = new OAApp();
    
    // 使全局函数可用
    window.showDetails = showDetails;
});