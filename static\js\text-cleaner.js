/**
 * 增强的文本清理工具
 * 用于处理OA系统中的乱码问题，特别是处理特殊字符、HTML实体和罗马数字
 */

// 将函数暴露到全局作用域
window.cleanHtml = function(content) {
    if (!content) return '';
    
    let text = content.toString();
    
    try {
        // 确保文本是字符串类型
        if (typeof text !== 'string') {
            text = String(text);
        }
        
        // 1. 清理特殊字符和符号
        const specialChars = ['!', '@', '#', '$', '%', '^', '&', '*', '{', '}', '\\', '|', '=', '+', '`', '~'];
        for (const char of specialChars) {
            text = text.split(char).join('');
        }
        
        // 2. 专门处理MS Office字体样式问题
        text = text.split('mso-bidi-font-family').join('');
        text = text.split('mso-').join('');
        text = text.split('font-family').join('');
        
        // 3. 处理常见HTML实体
        const htmlEntities = {
            'nbsp;': ' ',
            'lt;': '<',
            'gt;': '>',
            'amp;': '&',
            'quot;': '"',
            'apos;': "'",
            'ldquo;': '"',
            'rdquo;': '"',
            'lsquo;': "'",
            'rsquo;': "'",
            'hellip;': '...',
            'mdash;': '—',
            'ndash;': '–',
            'middot;': '·',
            'bull;': '•',
            'times;': '×',
            'divide;': '÷',
            'copy;': '©',
            'reg;': '®',
            'trade;': '™'
        };
        
        // 处理HTML实体
        for (const [entity, replacement] of Object.entries(htmlEntities)) {
            text = text.split('&' + entity).join(replacement);
            text = text.split(entity).join(replacement); // 处理没有&前缀的情况
        }
        
        // 4. 处理数字HTML实体
        text = text.replace(/&#(\d+);/g, (match, numStr) => {
            const num = parseInt(numStr, 10);
            return String.fromCharCode(num);
        });
        
        // 5. 处理MS Office条件注释和特殊标签
        text = text.replace(/<\[if[^\]]*\]>[\s\S]*?<\[endif\]>/gi, '');
        text = text.replace(/<!--\[if[^\]]*\]>[\s\S]*?<!\[endif\]-->/gi, '');
        text = text.replace(/<o:[^>]*>[\s\S]*?<\/o:[^>]*>/gi, '');
        text = text.replace(/<\/?o:[^>]*>/gi, '');
        text = text.replace(/<w:[^>]*>[\s\S]*?<\/w:[^>]*>/gi, '');
        text = text.replace(/<\/?w:[^>]*>/gi, '');
        text = text.replace(/<v:[^>]*>[\s\S]*?<\/v:[^>]*>/gi, '');
        text = text.replace(/<\/?v:[^>]*>/gi, '');
        
        // 6. 清理HTML标签
        text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
        text = text.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
        text = text.replace(/<meta[^>]*>/gi, '');
        text = text.replace(/<link[^>]*>/gi, '');
        text = text.replace(/<[^>]*>/g, '');
        
        // 7. 清理XML/HTML注释
        text = text.replace(/<!--[\s\S]*?-->/g, '');
        
        // 8. 保留罗马数字
        // 临时替换罗马数字为占位符
        let romanMap = {
            'Ⅰ': '__ROMAN_1__', 'Ⅱ': '__ROMAN_2__', 'Ⅲ': '__ROMAN_3__', 'Ⅳ': '__ROMAN_4__', 'Ⅴ': '__ROMAN_5__',
            'Ⅵ': '__ROMAN_6__', 'Ⅶ': '__ROMAN_7__', 'Ⅷ': '__ROMAN_8__', 'Ⅸ': '__ROMAN_9__', 'Ⅹ': '__ROMAN_10__',
            'ⅰ': '__ROMAN_11__', 'ⅱ': '__ROMAN_12__', 'ⅲ': '__ROMAN_13__', 'ⅳ': '__ROMAN_14__', 'ⅴ': '__ROMAN_15__',
            'ⅵ': '__ROMAN_16__', 'ⅶ': '__ROMAN_17__', 'ⅷ': '__ROMAN_18__', 'ⅸ': '__ROMAN_19__', 'ⅹ': '__ROMAN_20__'
        };
        
        for (const [roman, placeholder] of Object.entries(romanMap)) {
            text = text.split(roman).join(placeholder);
        }
        
        // 9. 清理控制字符和不可见字符（保留换行符、制表符等基本格式）
        text = text.replace(/[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]/g, '');
        
        // 10. 清理零宽字符
        text = text.replace(/[\u200b\u200c\u200d\u2060\ufeff]/g, '');
        
        // 11. 清理替换字符
        text = text.replace(/\ufffd/g, '');
        
        // 12. 清理重复的空白字符（保留换行符）
        while (text.includes('  ')) {
            text = text.split('  ').join(' ');
        }
        text = text.split('\n\n\n').join('\n\n');
        
        // 13. 清理重复的标点符号
        const punctuations = ['，', '。', '！', '？', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】', '《', '》'];
        for (const punct of punctuations) {
            const doublePunct = punct + punct;
            while (text.includes(doublePunct)) {
                text = text.split(doublePunct).join(punct);
            }
        }
        
        // 14. 恢复罗马数字
        for (const [roman, placeholder] of Object.entries(romanMap)) {
            text = text.split(placeholder).join(roman);
        }
        
        // 15. 最终清理
        text = text.trim();
        
        // 如果清理后文本为空，返回默认值
        if (!text || text.trim() === '') {
            return '无内容';
        }
        
        return text;
        
    } catch (error) {
        console.warn('文本清理失败:', error);
        return text || '无内容';
    }
}

/**
 * 清理文档标题
 * 使用相同的cleanHtml函数，但针对标题做一些特殊处理
 */
function cleanTitle(title) {
    if (!title) return '无标题';
    
    // 使用通用的清理函数
    let cleanedTitle = cleanHtml(title);
    
    // 标题特殊处理：移除多余的"关于"和"的通知"等重复内容
    if (cleanedTitle.includes('关于') && cleanedTitle.includes('的通知')) {
        // 避免重复的"关于...的通知"模式
        const parts = cleanedTitle.split('关于');
        if (parts.length > 2) {
            // 保留第一个"关于"和最后一个"的通知"
            cleanedTitle = '关于' + parts[parts.length - 1];
        }
    }
    
    return cleanedTitle;
}
